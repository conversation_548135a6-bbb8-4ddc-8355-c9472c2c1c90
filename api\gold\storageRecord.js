import request from '@/utils/request'

// 查询用户存料记录列表
export function listStorageRecord(query) {
  return request({
    url: '/gold/storageRecord/list',
    method: 'get',
    params: query
  })
}

// 查询用户存料记录详细
export function getStorageRecord(id) {
  return request({
    url: '/gold/storageRecord/' + id,
    method: 'get'
  })
}

// 新增用户存料记录
export function addStorageRecord(data) {
  return request({
    url: '/gold/storageRecord',
    method: 'post',
    data: data
  })
}

// 修改用户存料记录
export function updateStorageRecord(data) {
  return request({
    url: '/gold/storageRecord',
    method: 'put',
    data: data
  })
}

// 删除用户存料记录
export function delStorageRecord(id) {
  return request({
    url: '/gold/storageRecord/' + id,
    method: 'delete'
  })
}
