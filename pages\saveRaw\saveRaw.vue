<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false"
			lineWidth="30" lineColor="#d4af37" activeColor="#d4af37"></u-tabs>

		<view v-show="currentTab === 0">
			<!-- 黄金寄存说明卡片 -->
			<view class="info-card">
				<view class="info-header">
					<image class="gold-icon" src="/static/images/gold-icon.png" mode="aspectFit"></image>
					<text class="info-title">黄金寄存服务</text>
				</view>
				<text class="info-desc">安全专业的黄金寄存服务，为您的贵金属提供专业保管</text>
			</view>

			<view class="deposit-panel">
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<!-- 金条/金料选择 -->
					<u-form-item label="寄存类型" prop="type">
						<view class="type-selector">
							<view class="type-option" :class="{ active: form.type === 'bar' }" @click="selectType('bar')">
								<image class="type-icon" src="/static/images/goldBar.png" mode="aspectFit"></image>
								<text class="type-text">金条</text>
							</view>
							<view class="type-option" :class="{ active: form.type === 'material' }" @click="selectType('material')">
								<image class="type-icon" src="/static/images/goldRaw.png" mode="aspectFit"></image>
								<text class="type-text">金料</text>
							</view>
						</view>
					</u-form-item>

					<u-form-item label="寄存克重(g)" prop="weight">
						<u--input v-model="form.weight" placeholder="请输入您邮寄的黄金克重" type="digit"></u--input>
					</u-form-item>

					<u-form-item label="快递单号" prop="trackingNumber">
						<u--input v-model="form.trackingNumber" placeholder="请输入您的快递单号"></u--input>
					</u-form-item>

					<u-button type="primary" shape="circle" class="submit-btn" @click="submitDeposit">
						确认寄存
					</u-button>
				</u--form>
			</view>

			<view class="process-panel">
				<view class="panel-header">
					<image class="process-icon" src="/static/images/process-icon.png" mode="aspectFit"></image>
					<text class="panel-title">寄存流程</text>
				</view>
				<u-steps :current="0" activeColor="#d4af37" inactiveColor="#e6d7b7">
					<u-steps-item title="邮寄黄金" desc="安全包装邮寄"></u-steps-item>
					<u-steps-item title="仓库收货" desc="专业仓库接收"></u-steps-item>
					<u-steps-item title="专业检测" desc="精密仪器检测"></u-steps-item>
					<u-steps-item title="确认入库" desc="安全保管入库"></u-steps-item>
				</u-steps>
			</view>
		</view>

		<view v-show="currentTab === 1">
			<view class="history-panel">
				<view class="history-list">
					<view class="history-item" v-for="(item, index) in historyList" :key="item.id || index">
						<view class="item-header">
							<text class="item-id">订单号: {{ item.storageNumber }}</text>
							<u-tag
								:text="formatStatus(item.status)"
								:type="item.status === 'PENDING_RECEIPT' ? 'warning' :
									   item.status === 'INSPECTING' ? 'primary' :
									   item.status === 'STORED' ? 'success' : 'error'"
								size="mini">
							</u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">寄存类型:</text>
								<text class="info-value">{{ formatDepositType(item.depositType) }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">寄存克重:</text>
								<text class="info-value">{{ item.weightGrams }} g</text>
							</view>
							<view class="info-row" v-if="item.trackingNumberIn">
								<text class="info-label">快递单号:</text>
								<text class="info-value">{{ item.trackingNumberIn }}</text>
							</view>
							<view class="info-row">
								<text class="info-label">提交时间:</text>
								<text class="info-value">{{ item.createTime }}</text>
							</view>
							<view class="info-row" v-if="item.returnReason">
								<text class="info-label">退回原因:</text>
								<text class="info-value error-text">{{ item.returnReason }}</text>
							</view>
							<view class="info-row" v-if="item.trackingNumberOut">
								<text class="info-label">退回单号:</text>
								<text class="info-value">{{ item.trackingNumberOut }}</text>
							</view>
						</view>
					</view>

					<!-- 加载状态 -->
					<view class="load-status" v-if="loading">
						<u-loading-icon></u-loading-icon>
						<text class="load-text">加载中...</text>
					</view>

					<!-- 加载完成提示 -->
					<view class="load-status" v-if="finished && historyList.length > 0">
						<text class="load-text">已加载全部数据</text>
					</view>

					<!-- 空状态 -->
					<u-empty v-if="!loading && !historyList.length" mode="order" text="暂无寄存记录" marginTop="150"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addStorageRecord,listStorageRecord } from "@/api/gold/storageRecord.js"
	export default {
		data() {
			return {
				userId: this.$store.state.user.userId,
				tabsList: [{ name: '我要寄存' }, { name: '寄存记录' }],
				currentTab: 0,
				form: {
					type: 'bar', // 默认选择金条
					weight: '',
					trackingNumber: '',
				},
				// 寄存记录列表
				historyList: [],
				// 分页参数
				pageNum: 1,
				pageSize: 10,
				total: 0,
				loading: false,
				finished: false,
			};
		},
		methods: {
			// 选择寄存类型
			selectType(type) {
				this.form.type = type;
			},

			// 切换Tabs
			handleTabChange(item) {
				this.currentTab = item.index;
				if (this.currentTab === 1) {
					this.loadStorageRecords(true);
				}
			},

			// 加载寄存记录列表
			async loadStorageRecords(refresh = false) {
				if (this.loading) return;

				if (refresh) {
					this.pageNum = 1;
					this.historyList = [];
					this.finished = false;
				}

				if (this.finished) return;

				this.loading = true;

				try {
					const params = {
						userId: this.userId,
						pageNum: this.pageNum,
						pageSize: this.pageSize
					};

					const response = await listStorageRecord(params);

					if (response.code === 200) {
						const newRecords = response.rows || [];

						if (refresh) {
							this.historyList = newRecords;
						} else {
							this.historyList = [...this.historyList, ...newRecords];
						}

						this.total = response.total || 0;
						this.pageNum++;

						// 判断是否已加载完所有数据
						if (newRecords.length < this.pageSize) {
							this.finished = true;
						}
					} else {
						this.$modal.msgError(response.msg || '加载寄存记录失败');
					}
				} catch (error) {
					console.error('加载寄存记录失败:', error);
					this.$modal.msgError('加载寄存记录失败');
				} finally {
					this.loading = false;
				}
			},
			// 提交寄存信息
			async submitDeposit() {
				if (!this.form.type) {
					return this.$modal.msgError('请选择寄存类型');
				}
				if (!this.form.weight) {
					return this.$modal.msgError('请输入寄存克重');
				}
				if (!this.form.trackingNumber) {
					return this.$modal.msgError('请输入快递单号');
				}

				// 验证克重格式
				const weight = parseFloat(this.form.weight);
				if (isNaN(weight) || weight <= 0) {
					return this.$modal.msgError('请输入有效的寄存克重');
				}

				const typeText = this.form.type === 'bar' ? '金条' : '金料';
				this.$modal.loading(`正在提交${typeText}寄存申请...`);

				try {
					const params = {
						depositType: this.form.type === 'bar' ? 'BAR' : 'MATERIAL',
						weightGrams: weight,
						trackingNumberIn: this.form.trackingNumber
					};

					const response = await addStorageRecord(params);

					if (response.code === 200) {
						this.$modal.closeLoading();
						this.$modal.msgSuccess(`${typeText}寄存申请提交成功！`);

						// 清空表单
						this.form = { type: 'bar', weight: '', trackingNumber: '' };

						// 切换到历史记录页并刷新数据
						this.currentTab = 1;
						this.loadStorageRecords(true);
					} else {
						this.$modal.closeLoading();
						this.$modal.msgError(response.msg || '提交失败，请重试');
					}
				} catch (error) {
					this.$modal.closeLoading();
					console.error('提交寄存申请失败:', error);
					this.$modal.msgError('提交失败，请检查网络连接');
				}
			},

			// 格式化状态文本
			formatStatus(status) {
				const statusMap = {
					'PENDING_RECEIPT': '待收货',
					'INSPECTING': '检测中',
					'STORED': '已入库',
					'RETURNED': '已退回'
				};
				return statusMap[status] || status;
			},

			// 格式化寄存类型
			formatDepositType(type) {
				return type === 'BAR' ? '金条' : '金料';
			},

			// 下拉刷新
			onRefresh() {
				this.loadStorageRecords(true);
			},

			// 上拉加载更多
			onLoadMore() {
				if (!this.finished && !this.loading) {
					this.loadStorageRecords(false);
				}
			}
		},

		onLoad() {
			// 页面加载时不自动加载数据，等用户切换到寄存记录tab时再加载
		},

		onShow() {
			// 页面显示时刷新当前tab的数据
			if (this.currentTab === 1) {
				this.loadStorageRecords(true);
			}
		},

		onReachBottom() {
			// 触底加载更多
			if (this.currentTab === 1) {
				this.onLoadMore();
			}
		},

		onPullDownRefresh() {
			// 下拉刷新
			if (this.currentTab === 1) {
				this.onRefresh();
				setTimeout(() => {
					uni.stopPullDownRefresh();
				}, 1000);
			} else {
				uni.stopPullDownRefresh();
			}
		}
	}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	// 信息卡片样式
	.info-card {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		.info-header {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			.gold-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 20rpx;
			}

			.info-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #d4af37;
			}
		}

		.info-desc {
			font-size: 28rpx;
			color: #8b7355;
			line-height: 1.6;
		}
	}

	.deposit-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		// 类型选择器样式
		.type-selector {
			display: flex;
			gap: 20rpx;
			margin-top: 20rpx;

			.type-option {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 20rpx;
				border: 3rpx solid #e6d7b7;
				border-radius: 16rpx;
				background: #fefcf7;
				transition: all 0.3s ease;

				&.active {
					border-color: #d4af37;
					background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
					transform: translateY(-4rpx);
					box-shadow: 0 8rpx 20rpx rgba(212, 175, 55, 0.3);

					.type-text {
						color: #fff;
						font-weight: bold;
					}
				}

				.type-icon {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 16rpx;
				}

				.type-text {
					font-size: 28rpx;
					color: #8b7355;
					transition: color 0.3s ease;
				}
			}
		}

		.submit-btn {
			margin-top: 40rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}

	.process-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		.panel-header {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.process-icon {
				width: 50rpx;
				height: 50rpx;
				margin-right: 16rpx;
			}

			.panel-title {
				font-size: 34rpx;
				font-weight: bold;
				color: #d4af37;
			}
		}
	}

	.history-panel {
		padding: 20rpx;

		.history-list {
			.history-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;

				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 20rpx;
					border-bottom: 1px solid #f5f5f5;
					margin-bottom: 20rpx;

					.item-id {
						font-size: 26rpx;
						color: #909399;
					}
				}

				.item-body {
					.info-row {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 28rpx;
						color: #303133;

						&:not(:last-child) {
							margin-bottom: 16rpx;
						}

						.info-label {
							color: #606266;
						}
						.info-value {
							font-weight: 500;
						}
					}
				}
			}

			// 加载状态样式
			.load-status {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 40rpx 20rpx;

				.load-text {
					font-size: 26rpx;
					color: #909399;
					margin-top: 16rpx;
				}
			}

			// 错误文本样式
			.error-text {
				color: #f56c6c !important;
			}
		}
	}
</style>