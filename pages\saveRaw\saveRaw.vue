<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false" lineWidth="30"></u-tabs>
		
		<view v-show="currentTab === 0">
			<view class="deposit-panel">
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<u-form-item label="存料金条/金料(g)" prop="weight">
						<u--input v-model="form.weight" placeholder="请输入您邮寄的黄金克重" type="digit"></u--input>
					</u-form-item>
					<u-form-item label="快递单号" prop="trackingNumber">
						<u--input v-model="form.trackingNumber" placeholder="请输入您的快递单号"></u--input>
					</u-form-item>
					<u-button type="primary" shape="circle" class="submit-btn" text="确认提交" @click="submitDeposit"></u-button>
				</u--form>
			</view>

			<view class="process-panel">
				<h3 class="panel-title">存料流程</h3>
				<u-steps :current="0" activeColor="#c8a77f">
					<u-steps-item title="邮寄黄金"></u-steps-item>
					<u-steps-item title="仓库收货"></u-steps-item>
					<u-steps-item title="专业检测"></u-steps-item>
					<u-steps-item title="确认入库"></u-steps-item>
				</u-steps>
			</view>
		</view>

		<view v-show="currentTab === 1">
			<view class="history-panel">
				<view class="history-list">
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250713001</text>
							<u-tag text="待收货" type="warning" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">存料克重:</text>
								<text class="info-value">10.05 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">提交时间:</text>
								<text class="info-value">2025-07-13 10:30</text>
							</view>
						</view>
					</view>
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250712005</text>
							<u-tag text="检测中" type="primary" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">存料克重:</text>
								<text class="info-value">50.12 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">提交时间:</text>
								<text class="info-value">2025-07-12 15:00</text>
							</view>
						</view>
					</view>
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250710002</text>
							<u-tag text="已入库" type="success" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">存料克重:</text>
								<text class="info-value">20.00 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">入库时间:</text>
								<text class="info-value">2025-07-11 09:45</text>
							</view>
						</view>
					</view>
					<u-empty v-if="!historyList.length" mode="order" text="暂无存料记录" marginTop="150"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				tabsList: [{ name: '我要存料' }, { name: '存料记录' }],
				currentTab: 0,
				form: {
					weight: '',
					trackingNumber: '',
				},
				// 静态历史记录列表，用于演示
				historyList: [1, 2, 3], 
			};
		},
		methods: {
			// 切换Tabs
			handleTabChange(item) {
				this.currentTab = item.index;
				if (this.currentTab === 1 && this.historyList.length === 0) {
					// 在此调用您的 getHistoryList() API
					console.log("加载存料记录列表...");
				}
			},
			// 提交存料信息
			submitDeposit() {
				if (!this.form.weight) {
					return this.$modal.msgError('请输入存料克重');
				}
				if (!this.form.trackingNumber) {
					return this.$modal.msgError('请输入快递单号');
				}
				
				this.$modal.loading('正在提交...');
				
				// 模拟API请求
				setTimeout(() => {
					this.$modal.closeLoading();
					this.$modal.msgSuccess('提交成功！');
					// 可以在此清空表单，并切换到历史记录页
					this.form = { weight: '', trackingNumber: '' };
					this.handleTabChange({ index: 1 });
				}, 1000);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}

	.panel-title {
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		color: #303133;
	}

	.deposit-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		
		.submit-btn {
			margin-top: 40rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}
	
	.process-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
	}
	
	.history-panel {
		padding: 20rpx;
		
		.history-list {
			.history-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;
				
				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 20rpx;
					border-bottom: 1px solid #f5f5f5;
					margin-bottom: 20rpx;
					
					.item-id {
						font-size: 26rpx;
						color: #909399;
					}
				}
				
				.item-body {
					.info-row {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 28rpx;
						color: #303133;
						
						&:not(:last-child) {
							margin-bottom: 16rpx;
						}
						
						.info-label {
							color: #606266;
						}
						.info-value {
							font-weight: 500;
						}
					}
				}
			}
		}
	}
</style>