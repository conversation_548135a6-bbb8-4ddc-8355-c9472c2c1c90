<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false"
			lineWidth="30" lineColor="#d4af37" activeColor="#d4af37"></u-tabs>

		<view v-show="currentTab === 0">
			<!-- 黄金寄存说明卡片 -->
			<view class="info-card">
				<view class="info-header">
					<image class="gold-icon" src="/static/images/gold-icon.png" mode="aspectFit"></image>
					<text class="info-title">黄金寄存服务</text>
				</view>
				<text class="info-desc">安全专业的黄金寄存服务，为您的贵金属提供专业保管</text>
			</view>

			<view class="deposit-panel">
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<!-- 金条/金料选择 -->
					<u-form-item label="寄存类型" prop="type">
						<view class="type-selector">
							<view class="type-option" :class="{ active: form.type === 'bar' }" @click="selectType('bar')">
								<image class="type-icon" src="/static/images/goldBar.png" mode="aspectFit"></image>
								<text class="type-text">金条</text>
							</view>
							<view class="type-option" :class="{ active: form.type === 'material' }" @click="selectType('material')">
								<image class="type-icon" src="/static/images/goldRaw.png" mode="aspectFit"></image>
								<text class="type-text">金料</text>
							</view>
						</view>
					</u-form-item>

					<u-form-item label="寄存克重(g)" prop="weight">
						<u--input v-model="form.weight" placeholder="请输入您邮寄的黄金克重" type="digit"></u--input>
					</u-form-item>

					<u-form-item label="快递单号" prop="trackingNumber">
						<u--input v-model="form.trackingNumber" placeholder="请输入您的快递单号"></u--input>
					</u-form-item>

					<u-button type="primary" shape="circle" class="submit-btn" @click="submitDeposit">
						确认寄存
					</u-button>
				</u--form>
			</view>

			<view class="process-panel">
				<view class="panel-header">
					<image class="process-icon" src="/static/images/process-icon.png" mode="aspectFit"></image>
					<text class="panel-title">寄存流程</text>
				</view>
				<u-steps :current="0" activeColor="#d4af37" inactiveColor="#e6d7b7">
					<u-steps-item title="邮寄黄金" desc="安全包装邮寄"></u-steps-item>
					<u-steps-item title="仓库收货" desc="专业仓库接收"></u-steps-item>
					<u-steps-item title="专业检测" desc="精密仪器检测"></u-steps-item>
					<u-steps-item title="确认入库" desc="安全保管入库"></u-steps-item>
				</u-steps>
			</view>
		</view>

		<view v-show="currentTab === 1">
			<view class="history-panel">
				<view class="history-list">
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250713001</text>
							<u-tag text="待收货" type="warning" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">寄存克重:</text>
								<text class="info-value">10.05 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">提交时间:</text>
								<text class="info-value">2025-07-13 10:30</text>
							</view>
						</view>
					</view>
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250712005</text>
							<u-tag text="检测中" type="primary" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">寄存克重:</text>
								<text class="info-value">50.12 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">提交时间:</text>
								<text class="info-value">2025-07-12 15:00</text>
							</view>
						</view>
					</view>
					<view class="history-item">
						<view class="item-header">
							<text class="item-id">订单号: CL20250710002</text>
							<u-tag text="已入库" type="success" size="mini"></u-tag>
						</view>
						<view class="item-body">
							<view class="info-row">
								<text class="info-label">存料克重:</text>
								<text class="info-value">20.00 g</text>
							</view>
							<view class="info-row">
								<text class="info-label">入库时间:</text>
								<text class="info-value">2025-07-11 09:45</text>
							</view>
						</view>
					</view>
					<u-empty v-if="!historyList.length" mode="order" text="暂无寄存记录" marginTop="150"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addStorageRecord,listStorageRecord } from "@/api/gold/storageRecord.js"
	export default {
		data() {
			return {
				userId:this.$store.state.user.userId,
				tabsList: [{ name: '我要寄存' }, { name: '寄存记录' }],
				currentTab: 0,
				form: {
					type: 'bar', // 默认选择金条
					weight: '',
					trackingNumber: '',
				},
				// 静态历史记录列表，用于演示
				historyList: [1, 2, 3],
			};
		},
		methods: {
			// 选择寄存类型
			selectType(type) {
				this.form.type = type;
			},

			// 切换Tabs
			handleTabChange(item) {
				this.currentTab = item.index;
				if (this.currentTab === 1 && this.historyList.length === 0) {
					// 在此调用您的 getHistoryList() API
					console.log("加载寄存记录列表...");
				}
			},
			// 提交寄存信息
			submitDeposit() {
				if (!this.form.type) {
					return this.$modal.msgError('请选择寄存类型');
				}
				if (!this.form.weight) {
					return this.$modal.msgError('请输入寄存克重');
				}
				if (!this.form.trackingNumber) {
					return this.$modal.msgError('请输入快递单号');
				}

				const typeText = this.form.type === 'bar' ? '金条' : '金料';
				this.$modal.loading(`正在提交${typeText}寄存申请...`);

				// 模拟API请求
				setTimeout(() => {
					this.$modal.closeLoading();
					this.$modal.msgSuccess(`${typeText}寄存申请提交成功！`);
					// 可以在此清空表单，并切换到历史记录页
					this.form = { type: 'bar', weight: '', trackingNumber: '' };
					this.handleTabChange({ index: 1 });
				}, 1000);
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	// 信息卡片样式
	.info-card {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		.info-header {
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			.gold-icon {
				width: 60rpx;
				height: 60rpx;
				margin-right: 20rpx;
			}

			.info-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #d4af37;
			}
		}

		.info-desc {
			font-size: 28rpx;
			color: #8b7355;
			line-height: 1.6;
		}
	}

	.deposit-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		// 类型选择器样式
		.type-selector {
			display: flex;
			gap: 20rpx;
			margin-top: 20rpx;

			.type-option {
				flex: 1;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 20rpx;
				border: 3rpx solid #e6d7b7;
				border-radius: 16rpx;
				background: #fefcf7;
				transition: all 0.3s ease;

				&.active {
					border-color: #d4af37;
					background: linear-gradient(135deg, #d4af37 0%, #ffd700 100%);
					transform: translateY(-4rpx);
					box-shadow: 0 8rpx 20rpx rgba(212, 175, 55, 0.3);

					.type-text {
						color: #fff;
						font-weight: bold;
					}
				}

				.type-icon {
					width: 80rpx;
					height: 80rpx;
					margin-bottom: 16rpx;
				}

				.type-text {
					font-size: 28rpx;
					color: #8b7355;
					transition: color 0.3s ease;
				}
			}
		}

		.submit-btn {
			margin-top: 40rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}

	.process-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;

		.panel-header {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;

			.process-icon {
				width: 50rpx;
				height: 50rpx;
				margin-right: 16rpx;
			}

			.panel-title {
				font-size: 34rpx;
				font-weight: bold;
				color: #d4af37;
			}
		}
	}

	.history-panel {
		padding: 20rpx;

		.history-list {
			.history-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-bottom: 20rpx;

				.item-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 20rpx;
					border-bottom: 1px solid #f5f5f5;
					margin-bottom: 20rpx;

					.item-id {
						font-size: 26rpx;
						color: #909399;
					}
				}

				.item-body {
					.info-row {
						display: flex;
						justify-content: space-between;
						align-items: center;
						font-size: 28rpx;
						color: #303133;

						&:not(:last-child) {
							margin-bottom: 16rpx;
						}

						.info-label {
							color: #606266;
						}
						.info-value {
							font-weight: 500;
						}
					}
				}
			}
		}
	}
</style>