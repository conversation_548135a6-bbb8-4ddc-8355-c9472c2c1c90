<template>
	<view class="address-edit-container">
		<view class="form-container">
			<view class="form-item">
				<text class="form-label">收货人</text>
				<input
					class="form-input"
					v-model="form.recipientName"
					placeholder="请输入收货人姓名"
					placeholder-class="input-placeholder"
				/>
			</view>

			<view class="form-item">
				<text class="form-label">手机号码</text>
				<input
					class="form-input"
					v-model="form.phoneNumber"
					placeholder="请输入手机号码"
					type="number"
					placeholder-class="input-placeholder"
				/>
			</view>

			<view class="form-item">
				<text class="form-label">所在地区</text>
				<picker
					mode="region"
					:value="regionIndex"
					@change="regionChange"
					class="region-picker-input"
				>
					<view class="form-input region-input">
						<text class="region-text" :class="{ 'placeholder': !form.region }">
							{{ form.region || '请选择省市区' }}
						</text>
						<text class="arrow">></text>
					</view>
				</picker>
			</view>

			<view class="form-item">
				<text class="form-label">详细地址</text>
				<textarea
					class="form-textarea"
					v-model="form.detailAddress"
					placeholder="请输入详细地址（街道、门牌号等）"
					placeholder-class="input-placeholder"
				></textarea>
			</view>

			<view class="form-item">
				<text class="form-label">设为默认</text>
				<switch
					class="form-switch"
					:checked="form.isDefault"
					@change="switchChange"
					color="#b38e5d"
				/>
			</view>
		</view>

		<view class="button-container">
			<button
				class="save-button"
				@click="saveAddress"
				:disabled="loading"
			>
				{{ loading ? '保存中...' : (isEdit ? '保存修改' : '添加地址') }}
			</button>
		</view>


	</view>
</template>

<script>
	import { addUserAddress, getUserAddress, updateUserAddress } from "@/api/gold/userAddress.js"
	import { getToken } from "../../utils/auth";

	export default {
		data() {
			return {
				form: {
					recipientName: '',
					phoneNumber: '',
					region: '',
					detailAddress: '',
					isDefault: false
				},
				isEdit: false,
				addressId: null,
				loading: false,
				regionArray: [], // 地区选择器的值数组
				regionIndex: [0, 0, 0] // 地区选择器的索引数组
			}
		},
		onLoad(options) {
			if (options.id) {
				this.isEdit = true;
				this.addressId = options.id;
				this.getAddressDetail();
			}
		},
		methods: {
			// 获取地址详情
			async getAddressDetail() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					const response = await getUserAddress(this.addressId);
					uni.hideLoading();

					if (response.code === 200 && response.data) {
						const data = response.data;
						this.form = {
							recipientName: data.recipientName || '',
							phoneNumber: data.phoneNumber || '',
							region: data.region || '',
							detailAddress: data.detailAddress || '',
							isDefault: data.isDefault === 'Y'
						};
					} else {
						uni.showToast({
							title: response.msg || '获取地址详情失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('获取地址详情失败:', error);
					uni.showToast({
						title: '获取地址详情失败',
						icon: 'none'
					});
				}
			},
			// 开关变化
			switchChange(e) {
				this.form.isDefault = e.detail.value;
			},
			// 地区选择变化
			regionChange(e) {
				this.regionIndex = e.detail.value;
				this.regionArray = e.detail.code;
				// 将选中的地区组合成字符串
				const regions = e.detail.value;
				this.form.region = regions.join(' ');
			},
			// 表单验证
			validateForm() {
				if (!this.form.recipientName.trim()) {
					uni.showToast({
						title: '请输入收货人姓名',
						icon: 'none'
					});
					return false;
				}

				if (!this.form.phoneNumber.trim()) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}

				if (!/^1[3-9]\d{9}$/.test(this.form.phoneNumber)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none'
					});
					return false;
				}

				if (!this.form.region.trim()) {
					uni.showToast({
						title: '请选择所在地区',
						icon: 'none'
					});
					return false;
				}

				if (!this.form.detailAddress.trim()) {
					uni.showToast({
						title: '请输入详细地址',
						icon: 'none'
					});
					return false;
				}

				return true;
			},
			// 保存地址
			async saveAddress() {
				// 表单验证
				if (!this.validateForm()) {
					return;
				}

				try {
					this.loading = true;

					const addressData = {
						recipientName: this.form.recipientName,
						phoneNumber: this.form.phoneNumber,
						region: this.form.region,
						detailAddress: this.form.detailAddress,
						isDefault: this.form.isDefault ? 'Y' : 'N'
					};

					let response;
					if (this.isEdit) {
						// 编辑时需要传入id
						addressData.id = this.addressId;
						response = await updateUserAddress(addressData);
					} else {
						response = await addUserAddress(addressData);
					}

					this.loading = false;

					if (response.code === 200) {
						uni.showToast({
							title: this.isEdit ? '修改成功' : '添加成功',
							icon: 'success'
						});

						// 通知上一页面刷新数据
						this.notifyPreviousPage();

						setTimeout(() => {
							uni.navigateBack();
						}, 1500);
					} else {
						uni.showToast({
							title: response.msg || (this.isEdit ? '修改失败' : '添加失败'),
							icon: 'none'
						});
					}
				} catch (error) {
					this.loading = false;
					console.error('保存地址失败:', error);
					uni.showToast({
						title: this.isEdit ? '修改失败' : '添加失败',
						icon: 'none'
					});
				}
			},
			// 通知上一页面刷新数据
			notifyPreviousPage() {
				const pages = getCurrentPages();
				if (pages.length >= 2) {
					const prevPage = pages[pages.length - 2];
					// 如果上一页面是地址列表页面，调用其刷新方法
					if (prevPage.route === 'pages/addressList/addressList' && prevPage.$vm.getAddressList) {
						prevPage.$vm.needRefresh = true;
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: linear-gradient(135deg, #f8f4e6 0%, #fff 100%);
	}

	.address-edit-container {
		min-height: 100vh;
		padding: 40rpx 20rpx 120rpx;
	}

	.form-container {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 16rpx;
		padding: 0;
		margin-bottom: 40rpx;
		box-shadow: 0 4rpx 20rpx rgba(179, 142, 93, 0.1);
		backdrop-filter: blur(10rpx);
		overflow: hidden;
	}

	.form-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid rgba(179, 142, 93, 0.1);

		&:last-child {
			border-bottom: none;
		}

		.form-label {
			width: 140rpx;
			font-size: 28rpx;
			color: #303133;
			font-weight: 500;
		}

		.form-input {
			flex: 1;
			font-size: 28rpx;
			color: #303133;
			background: transparent;
			border: none;
			outline: none;
		}

		.form-textarea {
			flex: 1;
			font-size: 28rpx;
			color: #303133;
			background: transparent;
			border: none;
			outline: none;
			min-height: 80rpx;
			resize: none;
		}

		.region-input {
			display: flex;
			align-items: center;
			justify-content: space-between;
			cursor: pointer;

			.region-text {
				flex: 1;

				&.placeholder {
					color: #c0c4cc;
				}
			}

			.arrow {
				color: #c0c4cc;
				font-size: 24rpx;
			}
		}

		.form-switch {
			transform: scale(0.8);
		}
	}

	.input-placeholder {
		color: #c0c4cc;
	}

	.button-container {
		padding: 0 30rpx;
	}

	.save-button {
		width: 100%;
		height: 88rpx;
		background: linear-gradient(135deg, #b38e5d, #d4af37);
		border: none;
		border-radius: 44rpx;
		color: #fff;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;

		&:disabled {
			opacity: 0.6;
		}

		&:active {
			transform: scale(0.98);
		}
	}

	.region-picker-input {
		flex: 1;
	}
</style>