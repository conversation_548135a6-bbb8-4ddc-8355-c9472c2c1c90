<template>
	<view class="address-list-container">

		<!-- 地址列表 -->
		<view class="address-list" v-if="addressList.length > 0">
			<view
				class="address-item"
				v-for="(address, index) in addressList"
				:key="address.id"
				@click="selectAddress(address)"
			>
				<view class="address-content">
					<view class="address-header">
						<view class="user-info">
							<text class="name">{{ address.recipientName }}</text>
							<text class="phone">{{ address.phoneNumber }}</text>
						</view>
						<view class="default-tag" v-if="address.isDefault === 'Y'">默认</view>
					</view>
					<view class="address-detail">
						<text class="address-text">{{ address.region }} {{ address.detailAddress }}</text>
					</view>
				</view>
				<view class="address-actions">
					<view class="action-btn" @click.stop="editAddress(address)">
						<u-icon name="edit-pen" color="#b38e5d" size="16"></u-icon>
						<text>编辑</text>
					</view>
					<view class="action-btn" @click.stop="deleteAddress(address, index)">
						<u-icon name="trash" color="#f56c6c" size="16"></u-icon>
						<text>删除</text>
					</view>
					<view class="action-btn" @click.stop="setDefaultAddress(address)" v-if="address.isDefault !== 'Y'">
						<u-icon name="star" color="#ffa500" size="16"></u-icon>
						<text>设为默认</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view class="empty-state" v-else>
			<u-icon name="map" color="#c0c4cc" size="80"></u-icon>
			<text class="empty-text">暂无收货地址</text>
			<text class="empty-desc">请添加您的收货地址</text>
		</view>

		<!-- 添加地址按钮 -->
		<view class="add-address-btn" @click="addAddress">
			<u-icon name="plus" color="#fff" size="20"></u-icon>
			<text>添加新地址</text>
		</view>
	</view>
</template>

<script>
	import { listUserAddress, delUserAddress } from "@/api/gold/userAddress.js"
	import { getToken } from "../../utils/auth";

	export default {
		data() {
			return {
				addressList: [],
				fromPage: '', // 来源页面
				needRefresh: false ,// 是否需要刷新数据
				userId:this.$store.state.user.userId
			}
		},
		onLoad(options) {
			this.fromPage = options.from || '';
			this.getAddressList();
		},
		methods: {
			// 获取地址列表
			async getAddressList() {
				try {
					uni.showLoading({
						title: '加载中...'
					});

					const token = getToken();
					if (!token) {
						uni.hideLoading();
						uni.showToast({
							title: '请先登录',
							icon: 'none'
						});
						return;
					}

					// 传递userId参数
					const params = {
						userId: this.userId
					};

					const response = await listUserAddress(params);
					uni.hideLoading();

					if (response.code === 200) {
						// 根据接口返回的数据结构，地址列表在rows字段中
						this.addressList = response.rows || [];
					} else {
						uni.showToast({
							title: response.msg || '获取地址列表失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('获取地址列表失败:', error);
					uni.showToast({
						title: '获取地址列表失败',
						icon: 'none'
					});
				}
			},
			// 选择地址
			selectAddress(address) {
				if (this.fromPage === 'takeRaw') {
					// 如果是从提货页面来的，返回选中的地址
					const pages = getCurrentPages();
					const prevPage = pages[pages.length - 2];
					if (prevPage) {
						prevPage.$vm.selectedAddress = address;
					}
					uni.navigateBack();
				} else {
					// 其他情况，可以进入编辑页面
					this.editAddress(address);
				}
			},
			// 添加地址
			addAddress() {
				this.needRefresh = true; // 标记需要刷新
				uni.navigateTo({
					url: '/pages/addressEdit/addressEdit'
				});
			},
			// 编辑地址
			editAddress(address) {
				this.needRefresh = true; // 标记需要刷新
				uni.navigateTo({
					url: `/pages/addressEdit/addressEdit?id=${address.id}`
				});
			},
			// 删除地址
			deleteAddress(address, index) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个地址吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								uni.showLoading({
									title: '删除中...'
								});

								const response = await delUserAddress(address.id);
								uni.hideLoading();

								if (response.code === 200) {
									this.addressList.splice(index, 1);
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});
								} else {
									uni.showToast({
										title: response.msg || '删除失败',
										icon: 'none'
									});
								}
							} catch (error) {
								uni.hideLoading();
								console.error('删除地址失败:', error);
								uni.showToast({
									title: '删除失败',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			// 设置默认地址
			async setDefaultAddress(address) {
				// 临时提示功能开发中，等待后端API完善
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			}
		},
		onShow() {
			// 从编辑页面返回时检查是否需要刷新列表
			if (this.needRefresh) {
				this.getAddressList();
				this.needRefresh = false;
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: linear-gradient(135deg, #f8f4e6 0%, #fff 100%);
	}

	.address-list-container {
		min-height: 100vh;
		padding: 40rpx 0 120rpx 0;
	}

	.address-list {
		padding: 20rpx;
	}

	.address-item {
		background: rgba(255, 255, 255, 0.95);
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(179, 142, 93, 0.1);
		backdrop-filter: blur(10rpx);
		position: relative;
		overflow: hidden;

		&:active {
			background: rgba(248, 244, 230, 0.8);
		}

		.address-content {
			margin-bottom: 20rpx;

			.address-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;

				.user-info {
					display: flex;
					align-items: center;
					gap: 20rpx;

					.name {
						font-size: 32rpx;
						font-weight: bold;
						color: #303133;
					}

					.phone {
						font-size: 28rpx;
						color: #606266;
					}
				}

				.default-tag {
					background: linear-gradient(135deg, #b38e5d, #d4af37);
					color: #fff;
					font-size: 22rpx;
					padding: 6rpx 12rpx;
					border-radius: 12rpx;
					font-weight: 500;
				}
			}

			.address-detail {
				.address-text {
					font-size: 28rpx;
					color: #606266;
					line-height: 1.5;
				}
			}
		}

		.address-actions {
			display: flex;
			justify-content: flex-end;
			gap: 30rpx;
			padding-top: 20rpx;
			border-top: 1rpx solid rgba(179, 142, 93, 0.1);

			.action-btn {
				display: flex;
				align-items: center;
				gap: 8rpx;
				padding: 12rpx 16rpx;
				border-radius: 20rpx;
				background: rgba(179, 142, 93, 0.1);
				cursor: pointer;
				transition: all 0.3s ease;

				text {
					font-size: 24rpx;
					color: #606266;
				}

				&:active {
					background: rgba(179, 142, 93, 0.2);
					transform: scale(0.95);
				}
			}
		}
	}

	.empty-state {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 120rpx 40rpx;
		text-align: center;

		.empty-text {
			font-size: 32rpx;
			color: #909399;
			margin: 30rpx 0 16rpx;
			font-weight: 500;
		}

		.empty-desc {
			font-size: 26rpx;
			color: #c0c4cc;
		}
	}

	.add-address-btn {
		position: fixed;
		bottom: 40rpx;
		left: 30rpx;
		right: 30rpx;
		height: 88rpx;
		background: linear-gradient(135deg, #b38e5d, #d4af37);
		border-radius: 44rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 16rpx;
		box-shadow: 0 8rpx 32rpx rgba(179, 142, 93, 0.3);
		cursor: pointer;
		transition: all 0.3s ease;

		text {
			color: #fff;
			font-size: 32rpx;
			font-weight: 500;
		}

		&:active {
			transform: scale(0.98);
			box-shadow: 0 4rpx 16rpx rgba(179, 142, 93, 0.4);
		}
	}
</style>
