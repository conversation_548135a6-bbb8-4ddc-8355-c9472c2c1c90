<template>
	<view class="page-container">
		<view class="price-section">
			<view class="price-card">
				<view class="price-title-row">
					<text class="price-title">实时原料金价</text>
					<text class="price-tag">价格实时变动</text>
				</view>
				<view class="price-value" :class="{ 'price-animate': priceAnimate }">
					<text class="currency">¥</text>
					<text class="price-integer">{{ priceInteger }}</text>
					<text class="price-decimal">.{{ priceDecimal }} /g</text>
				</view>
			</view>
		</view>

		<view class="subsection-wrapper">
			<u-subsection
				:list="tabsList"
				:current="currentTab"
				@change="handleTabChange"
				activeColor="#c8a77f"
			></u-subsection>
		</view>

		<view class="content-wrapper" v-if="currentTab === 0">
			<view class="purchase-panel">
				<u--form :model="barForm" ref="uBarForm" labelPosition="left" labelWidth="auto">
					<u-form-item label="选择购买克重(g)" prop="weight" labelPosition="top">
						<view class="weight-options-wrapper">
							<view
								class="weight-tag"
								v-for="weight in barWeightOptions"
								:key="weight"
								:class="{ 'active': barForm.weight === weight }"
								@click="selectBarWeight(weight)"
							>
								{{ weight }}g
							</view>
						</view>
					</u-form-item>

				</u--form>

				<view class="form-row">
					<text class="form-label">购买数量</text>
					<u-number-box v-model="purchaseQuantity" integer></u-number-box>
				</view>

				<view class="form-row">
					<text class="form-label">实付总金额</text>
					<text class="total-amount-value">¥ {{ totalAmount }}</text>
				</view>

				<view class="form-row recharge-row" @click="goToRecharge">
					<text class="form-label">余额充值</text>
					<view class="recharge-link">
						<text>前往充值</text>
						<u-icon name="arrow-right" color="#909399" size="14"></u-icon>
					</view>
				</view>
			</view>
			
			<view class="purchase-tip">
				*购买后自动存入寄存，可随时提料和卖出
			</view>

			<view class="agreement-section">
				<view class="agreement-checkbox-wrapper" @click="toggleAgreement">
					<view class="checkbox-icon" :class="{ 'checked': isAgreementChecked }">
						<u-icon v-if="isAgreementChecked" name="checkmark" color="#fff" size="16"></u-icon>
					</view>
					<text class="agreement-text">
						阅读并同意<text class="agreement-link" @click.stop="viewAgreement">《贵金属购销服务协议》</text>
					</text>
				</view>
			</view>

			<view class="payment-button-group">
				<view class="payment-option balance-payment" :class="{ 'disabled': !isAgreementChecked }" @click="handleBalancePayment">
					<text class="payment-text">余额支付</text>
					<text class="payment-subtext">当前余额: {{ userBalance }}元</text>
				</view>
			</view>
		</view>

		<view class="content-wrapper" v-if="currentTab === 1">
			<view class="material-panel">
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<u-form-item label="选择购买克重(g)" prop="weight" labelPosition="top">
						<view class="weight-options-wrapper">
							<view
								class="weight-tag"
								v-for="weight in weightOptions"
								:key="weight"
								:class="{ 'active': form.weight === weight }"
								@click="selectWeight(weight)"
							>
								{{ weight }}g
							</view>
						</view>
					</u-form-item>

					<u-form-item label="预估总价">
						<text class="total-price">¥ {{ estimatedPrice }}</text>
					</u-form-item>

					<view class="purchase-tip-material">
						*购买后自动存入寄存，可随时提料和卖出
					</view>

					<view class="agreement-section-material">
						<view class="agreement-checkbox-wrapper" @click="toggleMaterialAgreement">
							<view class="checkbox-icon" :class="{ 'checked': materialAgreementChecked }">
								<u-icon v-if="materialAgreementChecked" name="checkmark" color="#fff" size="16"></u-icon>
							</view>
							<text class="agreement-text">
								阅读并同意<text class="agreement-link" @click.stop="viewAgreement">《贵金属购销服务协议》</text>
							</text>
						</view>
					</view>

					<view class="payment-button-group-material">
						<view class="payment-option balance-payment" :class="{ 'disabled': !materialAgreementChecked }" @click="handleMaterialBalancePayment">
							<text class="payment-text">余额支付</text>
							<text class="payment-subtext">当前余额: {{ userBalance }}元</text>
						</view>
					</view>
				</u--form>
			</view>
		</view>

		<view class="notice-panel">
			<h4 class="notice-title">购买须知</h4>
			<view class="notice-content">
				<p>1. 价格说明：页面金价为实时原料价，金条/金饰品价格包含工艺费。</p>
				<p>2. 交易流程：下单成功后，请在15分钟内完成支付以锁定价格。</p>
				<p>3. 物流发货：所有订单均采用顺丰保价服务，确保您的资产安全。</p>
			</view>
		</view>

		<!-- 自定义购买确认弹框 -->
		<u-modal :show="showConfirmModal" title="买入确认" :show-cancel-button="true"
			confirm-text="确认购买" cancel-text="取消" @confirm="confirmPurchase" @cancel="cancelPurchase">
			<view class="confirm-content">
				<view class="confirm-row">
					<text class="confirm-label">交易类别：</text>
					<text class="confirm-value">AU9999</text>
				</view>
				<view class="confirm-row">
					<text class="confirm-label">实时金价：</text>
					<text class="confirm-value">{{ goldPrice }}元/克</text>
				</view>
				<view class="confirm-row">
					<text class="confirm-label">交易克重：</text>
					<text class="confirm-value">{{ confirmData.weightGrams }}克</text>
				</view>
				<view class="confirm-row">
					<text class="confirm-label">金料总价：</text>
					<text class="confirm-value">{{ confirmData.totalPrice }}元</text>
				</view>
				<view class="confirm-row">
					<text class="confirm-label">所需押金：</text>
					<text class="confirm-value">{{ confirmData.deposit }}元</text>
				</view>
			</view>
		</u-modal>

	</view>
</template>

<script>
	import { getUser } from "@/api/system/user.js"
	import { getToken } from "../../utils/auth";
	import { addGoldOrder } from "@/api/gold/goldOrder.js"
	export default {
		data() {
			return {
				tabsList: ['购买金条', '购买金料'],
				currentTab: 0,
				// --- 新增：购买金条所需数据 ---
				purchaseQuantity: 1, // 购买数量
				agreementChecked: false, // 协议勾选状态
				materialAgreementChecked: false, // 购买金料协议勾选状态
				goldPrice: 548.10, // 实时金价（从price-value中提取）
				priceTimer: null, // 价格刷新定时器
				priceAnimate: false, // 价格动画状态
				userBalance: '0.00', // 用户余额
				// 金条表单数据
				barForm: {
					weight: '1' // 默认选择1克
				},
				// 金条克重选项（只有1克）
				barWeightOptions: ['1'],
				// --------------------------
				
				// 购买金料的表单
				form: {
					weight: '', // 存储选中的克重
				},
				estimatedPrice: '0.00',
				weightOptions: [100, 10, 1, 0.1, 0.01, 0.001],
				// 静态金条列表数据（此部分在视图中已不再使用，但保留在脚本中以备将来之需）
				goldBarList: [
					{ id: 1, name: '传承系列·高升金条', weight: 10, purity: 'Au 999.9', image: '/static/images/gold-bar-1.png' },
					{ id: 2, name: '传承系列·高升金条', weight: 20, purity: 'Au 999.9', image: '/static/images/gold-bar-1.png' },
					{ id: 3, name: '经典投资金条', weight: 50, purity: 'Au 999.9', image: '/static/images/gold-bar-2.png' },
					{ id: 4, name: '经典投资金条', weight: 100, purity: 'Au 999.9', image: '/static/images/gold-bar-2.png' },
				],
				userId: this.$store.state.user.userId,
				// 自定义确认弹框相关数据
				showConfirmModal: false,
				confirmData: {
					orderType: '',
					weightGrams: 0,
					quantity: 0,
					totalPrice: '0.00',
					deposit: '0.00'
				}
			};
		},
		computed: {
			// --- 新增：计算实付总金额 ---
			totalAmount() {
				// 每条金条固定1克
				const weightPerBar = 1;
				const amount = this.goldPrice * weightPerBar * this.purchaseQuantity;
				return amount.toFixed(2);
			},
			// 价格整数部分
			priceInteger() {
				return Math.floor(this.goldPrice).toString();
			},
			// 价格小数部分
			priceDecimal() {
				const decimal = (this.goldPrice % 1).toFixed(2).substring(2);
				return decimal;
			},
			// 检查协议是否已勾选
			isAgreementChecked() {
				return this.agreementChecked;
			}
		},
		onLoad() {
			this.getGoldPrice(); // 初始获取黄金价格
			this.startPriceTimer(); // 启动定时器
			this.getUserInfo(); // 获取用户信息
		},
		onUnload() {
			// 页面卸载时清除定时器
			if (this.priceTimer) {
				clearInterval(this.priceTimer);
				this.priceTimer = null;
			}
		},
		methods: {
			// 获取黄金价格
			async getGoldPrice() {
				try {
					const response = await uni.request({
						url: 'https://goldapi.szzjtech.cn/jjgold',
						method: 'GET',
						timeout: 10000
					});

					// uni.request返回的是数组，第二个元素是实际响应数据
					const data = response[1];

					if (data && data.statusCode === 200 && data.data && data.data.data.items) {
						const items = data.data.data.items;

						if (items['1']) {
							// 黄金销售价格 (askprice)
							const newPrice = parseFloat(items['1'].askprice) || 548.10;

							// 使用动画更新价格
							this.animatePrice(newPrice);
						}
					}
				} catch (error) {
					console.error('获取黄金价格失败:', error);
				}
			},
			// 启动价格刷新定时器
			startPriceTimer() {
				// 每5秒刷新一次价格
				this.priceTimer = setInterval(() => {
					this.getGoldPrice();
				}, 5000);
			},
			// 价格动画更新
			animatePrice(newPrice) {
				if (this.goldPrice !== newPrice) {
					// 更新价格值
					this.goldPrice = newPrice;

					// 触发动画
					this.$nextTick(() => {
						this.priceAnimate = true;

						// 动画结束后移除类
						setTimeout(() => {
							this.priceAnimate = false;
						}, 600);
					});
				}
			},
			handleTabChange(index) {
				this.currentTab = index;
			},
			selectWeight(weight) {
				this.form.weight = weight;
				this.calculatePrice();
			},
			calculatePrice() {
				const weight = parseFloat(this.form.weight);
				if (isNaN(weight) || weight <= 0) {
					this.estimatedPrice = '0.00';
					return;
				}
				this.estimatedPrice = (this.goldPrice * weight).toFixed(2);
			},
			// --- 新增：跳转到充值页面的方法（示例） ---
			goToRecharge(){
				uni.navigateTo({
					url:"/pages/payment/payment"
				})
			},
			// 获取用户信息
			async getUserInfo() {
				try {
					const token = getToken();
					if (!token || !this.userId) {
						this.userBalance = '0.00';
						return;
					}

					// 调用获取用户信息的API，传入userId
					const response = await getUser(this.userId);
					if (response.code === 200 && response.data) {
						this.userBalance = response.data.money || '0.00';
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
					this.userBalance = '0.00';
				}
			},
			// 切换协议勾选状态
			toggleAgreement() {
				this.agreementChecked = !this.agreementChecked;
			},
			// 切换购买金料协议勾选状态
			toggleMaterialAgreement() {
				this.materialAgreementChecked = !this.materialAgreementChecked;
			},
			// 选择金条克重
			selectBarWeight(weight) {
				this.barForm.weight = weight;
				this.calculateTotal(); // 重新计算总金额
			},
			// 查看协议
			viewAgreement() {
				uni.navigateTo({
					url: '/pages/agreement/metalTradingAgreement'
				});
			},
			// 余额支付处理（购买金条）
			handleBalancePayment() {
				console.log('handleBalancePayment 被调用');
				console.log('isAgreementChecked:', this.isAgreementChecked);
				console.log('agreementChecked:', this.agreementChecked);

				if (!this.isAgreementChecked) {
					uni.showToast({
						title: '请先同意贵金属购销服务协议',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				console.log('准备显示弹框');
				// 弹出确认购买弹框
				this.showPurchaseConfirm('BAR');
			},
			// 余额支付处理（购买金料）
			handleMaterialBalancePayment() {
				if (!this.materialAgreementChecked) {
					uni.showToast({
						title: '请先同意贵金属购销服务协议',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				if (!this.form.weight) {
					uni.showToast({
						title: '请选择购买克重',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				// 弹出确认购买弹框
				this.showPurchaseConfirm('MATERIAL');
			},
			// 显示购买确认弹框
			showPurchaseConfirm(orderType) {
				console.log('showPurchaseConfirm 被调用，orderType:', orderType);

				let weightGrams, quantity, totalPrice, deposit;

				if (orderType === 'BAR') {
					// 购买金条
					quantity = this.purchaseQuantity;
					weightGrams = quantity * 1; // 每条金条1克
					totalPrice = this.totalAmount;
				} else {
					// 购买金料
					quantity = 1; // 金料固定数量为1
					weightGrams = parseFloat(this.form.weight);
					totalPrice = this.estimatedPrice;
				}

				// 计算押金（假设押金为总价的10%，可根据实际业务调整）
				deposit = (parseFloat(totalPrice) * 0.1).toFixed(2);

				console.log('计算结果:', { weightGrams, quantity, totalPrice, deposit });

				// 设置确认弹框数据
				this.confirmData = {
					orderType: orderType,
					weightGrams: weightGrams,
					quantity: quantity,
					totalPrice: totalPrice,
					deposit: deposit
				};

				console.log('设置 showConfirmModal = true');
				// 显示自定义确认弹框
				this.showConfirmModal = true;

				console.log('showConfirmModal 当前值:', this.showConfirmModal);

				// 如果自定义弹框不工作，使用原生弹框作为备选方案
				setTimeout(() => {
					if (this.showConfirmModal) {
						console.log('自定义弹框可能有问题，使用原生弹框');
						this.showConfirmModal = false;
						this.showNativeConfirm(orderType, weightGrams, quantity, totalPrice, deposit);
					}
				}, 100);
			},

			// 确认购买
			confirmPurchase() {
				console.log('确认购买被点击');
				this.showConfirmModal = false;
				this.createGoldOrder(
					this.confirmData.orderType,
					this.confirmData.weightGrams,
					this.confirmData.quantity
				);
			},

			// 取消购买
			cancelPurchase() {
				console.log('取消购买被点击');
				this.showConfirmModal = false;
			},

			// 原生弹框备选方案
			showNativeConfirm(orderType, weightGrams, quantity, totalPrice, deposit) {
				const content = `交易类别：AU9999　　实时金价：${this.goldPrice}元/克　　交易克重：${weightGrams}克　　金料总价：${totalPrice}元　　所需押金：${deposit}元`;

				uni.showModal({
					title: '买入确认',
					content: content,
					confirmText: '确认购买',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.createGoldOrder(orderType, weightGrams, quantity);
						}
					}
				});
			},
			// 创建黄金订单
			async createGoldOrder(orderType, weightGrams, quantity) {
				uni.showLoading({
					title: '正在下单...'
				});

				try {
					const orderData = {
						orderType: orderType,
						weightGrams: weightGrams,
						quantity: quantity,
						paymentMethod: '余额支付'
					};

					console.log('发送订单数据:', orderData);

					const response = await addGoldOrder(orderData);

					console.log('API响应:', response);

					uni.hideLoading();

					if (response && response.code === 200) {
						uni.showToast({
							title: '下单成功',
							icon: 'success',
							duration: 2000
						});

						// 下单成功后的处理
						setTimeout(() => {
							// 可以跳转到订单详情页或者刷新用户余额
							this.getUserInfo(); // 刷新用户余额

							// 重置表单
							if (orderType === 'BAR') {
								this.purchaseQuantity = 1;
								this.agreementChecked = false;
							} else {
								this.form.weight = '';
								this.materialAgreementChecked = false;
								this.calculatePrice();
							}
						}, 2000);
					} else if (response && response.code === 500 && response.msg && response.msg.includes('余额不足')) {
						// 余额不足的特殊处理
						uni.showModal({
							title: '余额不足',
							content: response.msg + '\n是否前往充值？',
							confirmText: '去充值',
							cancelText: '取消',
							success: (modalRes) => {
								if (modalRes.confirm) {
									// 跳转到充值页面
									uni.navigateTo({
										url: '/pages/payment/payment'
									});
								}
							}
						});
					} else {
						// 其他错误情况
						const errorMsg = (response && response.msg) || '下单失败';
						uni.showToast({
							title: errorMsg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					uni.hideLoading();

					console.log('捕获到异常:', error);
					console.log('异常类型:', typeof error);

					// 如果error是字符串"500"或数字500，说明是HTTP状态码500（余额不足）
					if (error === 500 || error === '500' || String(error) === '500') {
						uni.showModal({
							title: '余额不足',
							content: '账户余额不足，请先充值\n是否前往充值？',
							confirmText: '去充值',
							cancelText: '取消',
							success: (modalRes) => {
								if (modalRes.confirm) {
									// 跳转到充值页面
									uni.navigateTo({
										url: '/pages/payment/payment'
									});
								}
							}
						});
					} else {
						// 其他异常
						uni.showToast({
							title: '下单失败，请重试',
							icon: 'none',
							duration: 2000
						});
					}
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	// --- 保留所有原有样式 ---
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
		padding-bottom: 40rpx;
	}

	.price-section {
		padding: 20rpx;
		
		.price-card {
			padding: 30rpx;
			border-radius: 16rpx;
			color: #8f6f40;
			background: linear-gradient(to right, #fcf1e0, #f8e9d0);
			border: 1px solid #f0e0c8;
			
			.price-title-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;
				
				.price-title {
					font-size: 30rpx;
					font-weight: bold;
				}
				
				.price-tag {
					font-size: 22rpx;
					background-color: rgba(200, 167, 127, 0.1);
					padding: 4rpx 10rpx;
					border-radius: 6rpx;
				}
			}
			
			.price-value {
				font-weight: bold;
				color: #c42626;
				transition: all 0.3s ease;
				position: relative;

				.currency {
					font-size: 32rpx;
					margin-right: 4rpx;
				}
				.price-integer {
					font-size: 56rpx;
				}
				.price-decimal {
					font-size: 32rpx;
				}

				// 价格变化动画
				&.price-animate {
					animation: priceChange 0.6s ease-in-out;
				}
			}
		}
	}

	// 价格变化动画关键帧
	@keyframes priceChange {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		25% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.6;
			color: #f39c12 !important;
		}
		75% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.subsection-wrapper {
		padding: 0 20rpx 20rpx;
	}
	
	.content-wrapper {
		padding: 0 20rpx;
	}

	.material-panel {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 16rpx;
		
		.weight-options-wrapper {
			display: flex;
			flex-wrap: wrap;
			gap: 20rpx;
			
			.weight-tag {
				background-color: #f5f5f5;
				color: #606266;
				padding: 16rpx 0;
				border-radius: 8rpx;
				font-size: 28rpx;
				transition: all 0.2s;
				text-align: center;
				width: calc((100% - 40rpx) / 3);
				
				&.active {
					background-color: #c8a77f;
					color: #fff;
					font-weight: 500;
				}
			}
		}

		.total-price {
			font-size: 36rpx;
			color: #fa3534;
			font-weight: bold;
		}
		
		.submit-btn {
			margin-top: 40rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}
	
	.notice-panel {
		padding: 40rpx 30rpx 20rpx;
		// ... 其他样式 ...
	}
	// --- 以上为保留样式 ---


	// --- 以下为新增和修改的样式 ---
	.purchase-panel {
		background-color: #fff;
		padding: 10rpx 30rpx;
		border-radius: 16rpx;
	}

	.form-row {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 28rpx 0;
		border-bottom: 1px solid #f5f5f5;

		&:last-child {
			border-bottom: none;
		}

		.form-label {
			font-size: 30rpx;
			color: #303133;
		}
		
		.form-value {
			font-size: 30rpx;
			color: #606266;
		}

		.amount-value {
			font-size: 34rpx;
			color: #c42626;
			font-weight: bold;
		}
		
		&.recharge-row {
			cursor: pointer;
			.recharge-link {
				display: flex;
				align-items: center;
				color: #909399;
				font-size: 28rpx;
			}
		}
	}

	.purchase-tip {
		font-size: 24rpx;
		color: #c42626;
		margin-top: 24rpx;
		padding: 0 10rpx;
	}

	.agreement-section {
		margin-top: 24rpx;
		padding: 0 10rpx;

		.agreement-checkbox-wrapper {
			display: flex;
			align-items: center;
			cursor: pointer;
		}

		.checkbox-icon {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #ddd;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			transition: all 0.3s ease;

			&.checked {
				background-color: #c8a77f;
				border-color: #c8a77f;
			}
		}

		.agreement-text {
			font-size: 26rpx;
			color: #606266;
			flex: 1;
		}

		.agreement-link {
			color: #c42626;
			text-decoration: underline;
		}
	}

	// 实付总金额样式
	.total-amount-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #e74c3c;
		margin-top: 10rpx;
	}

	// 购买金条克重选择样式
	.weight-options-wrapper {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
		margin-top: 20rpx;

		.weight-tag {
			padding: 16rpx 32rpx;
			border: 2rpx solid #e0e0e0;
			border-radius: 8rpx;
			font-size: 28rpx;
			color: #606266;
			background-color: #fff;
			cursor: pointer;
			transition: all 0.3s ease;

			&.active {
				border-color: #c8a77f;
				background-color: #c8a77f;
				color: #fff;
			}

			&:hover {
				border-color: #c8a77f;
			}
		}
	}

	// 购买金料相关样式
	.purchase-tip-material {
		font-size: 24rpx;
		color: #c42626;
		margin-top: 24rpx;
		padding: 0 10rpx;
	}

	.agreement-section-material {
		margin-top: 24rpx;
		padding: 0 10rpx;

		.agreement-checkbox-wrapper {
			display: flex;
			align-items: center;
			cursor: pointer;
		}

		.checkbox-icon {
			width: 32rpx;
			height: 32rpx;
			border: 2rpx solid #ddd;
			border-radius: 50%;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			transition: all 0.3s ease;

			&.checked {
				background-color: #c8a77f;
				border-color: #c8a77f;
			}
		}

		.agreement-text {
			font-size: 26rpx;
			color: #606266;
			flex: 1;
		}

		.agreement-link {
			color: #c42626;
			text-decoration: underline;
		}
	}

	.payment-button-group-material {
		margin-top: 40rpx;

		.payment-option {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			text-align: center;
			border-radius: 50px;
			font-size: 28rpx;
			border: 1px solid #f0e0c8;
			cursor: pointer;
			transition: all 0.3s ease;

			.payment-text {
				font-size: 30rpx;
			}

			.payment-subtext {
				font-size: 22rpx;
				margin-top: 4rpx;
			}
		}

		.balance-payment {
			background-color: #ebb879;
			color: #fff;

			&.disabled {
				background-color: #d0d0d0;
				color: #999;
				cursor: not-allowed;
				opacity: 0.6;
			}
		}
	}

	.payment-button-group {
		margin-top: 40rpx;

		.payment-option {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 20rpx;
			text-align: center;
			border-radius: 50px;
			font-size: 28rpx;
			border: 1px solid #f0e0c8;
			cursor: pointer;
			transition: all 0.3s ease;

			.payment-text {
				font-size: 30rpx;
			}

			.payment-subtext {
				font-size: 22rpx;
				margin-top: 4rpx;
			}
		}

		.balance-payment {
			background-color: #ebb879;
			color: #fff;

			&.disabled {
				background-color: #d0d0d0;
				color: #999;
				cursor: not-allowed;
				opacity: 0.6;
			}
		}
	}

	// 自定义确认弹框样式
	.confirm-content {
		padding: 20rpx 0;

		.confirm-row {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 16rpx 0;
			border-bottom: 1rpx solid #f5f5f5;

			&:last-child {
				border-bottom: none;
			}

			.confirm-label {
				font-size: 28rpx;
				color: #606266;
				font-weight: 500;
			}

			.confirm-value {
				font-size: 28rpx;
				color: #303133;
				font-weight: bold;

				&:last-child {
					color: #d4af37;
				}
			}
		}
	}

</style>