<template>
	<view class="agreement-container">
		<u-navbar
			:placeholder="true"
			:fixed="true"
			:safeAreaInsetTop="true"
			:border="false"
			bgColor="#f8f4e6"
			title="贵金属购销服务协议"
			:titleStyle="titleStyle"
			leftIcon="arrow-left"
			:autoBack="true"
		></u-navbar>

		<view class="agreement-content">
			<view class="agreement-header">
				<text class="agreement-title">贵金属购销服务协议</text>
				<text class="update-time">更新时间：2025年7月15日</text>
			</view>

			<view class="agreement-body">
				<view class="section">
					<text class="section-title">第一条 协议双方</text>
					<text class="section-content">
						甲方：御禧黄金（以下简称"平台"）
						乙方：用户（以下简称"用户"或"您"）
					</text>
				</view>

				<view class="section">
					<text class="section-title">第二条 服务内容</text>
					<text class="section-content">
						1. 平台为用户提供贵金属（包括但不限于黄金、白银等）的购买、销售、寄存等服务。
						2. 平台提供实时价格查询、在线交易、物流配送等配套服务。
						3. 平台保证所售贵金属的品质和纯度符合国家标准。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第三条 价格与支付</text>
					<text class="section-content">
						1. 贵金属价格以平台实时显示价格为准，价格可能随市场波动而变化。
						2. 用户下单后需在15分钟内完成支付，逾期订单将自动取消。
						3. 支持余额支付等多种支付方式。
						4. 所有交易均需实名认证，确保交易安全。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第四条 寄存服务</text>
					<text class="section-content">
						1. 用户购买的贵金属可选择寄存在平台指定的专业保管机构。
						2. 寄存期间，平台负责贵金属的安全保管，并提供相应的保险保障。
						3. 用户可随时申请提取寄存的贵金属，平台将在3个工作日内安排发货。
						4. 寄存服务免费，但提取时需承担相应的物流费用。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第五条 回购服务</text>
					<text class="section-content">
						1. 平台提供贵金属回购服务，回购价格以平台实时显示价格为准。
						2. 回购的贵金属需符合平台的品质要求，经检测合格后方可回购。
						3. 回购款项将在确认收货并检测合格后3个工作日内到账。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第六条 用户权利与义务</text>
					<text class="section-content">
						1. 用户有权了解所购贵金属的详细信息，包括品质、重量、纯度等。
						2. 用户应提供真实、准确的个人信息，并及时更新。
						3. 用户应妥善保管账户信息，不得将账户转让给他人使用。
						4. 用户应遵守国家相关法律法规，不得利用平台进行违法活动。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第七条 平台权利与义务</text>
					<text class="section-content">
						1. 平台有权对用户的交易行为进行监督和管理。
						2. 平台有义务保护用户的个人信息和交易数据安全。
						3. 平台应提供优质的客户服务，及时处理用户的咨询和投诉。
						4. 平台有权根据市场情况和业务需要调整服务内容和价格。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第八条 风险提示</text>
					<text class="section-content">
						1. 贵金属价格受市场因素影响，存在价格波动风险。
						2. 投资贵金属存在一定风险，用户应根据自身情况谨慎投资。
						3. 平台不对因市场价格波动造成的损失承担责任。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第九条 争议解决</text>
					<text class="section-content">
						1. 因本协议产生的争议，双方应友好协商解决。
						2. 协商不成的，可向平台所在地人民法院提起诉讼。
					</text>
				</view>

				<view class="section">
					<text class="section-title">第十条 协议生效</text>
					<text class="section-content">
						1. 本协议自用户点击同意之日起生效。
						2. 平台有权根据业务发展需要修改本协议，修改后的协议将在平台公布。
						3. 用户继续使用平台服务视为同意修改后的协议。
					</text>
				</view>
			</view>

			<view class="agreement-footer">
				<text class="footer-text">御禧黄金</text>
				<text class="footer-date">2025年7月15日</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				titleStyle: {
					fontSize: '32rpx',
					fontWeight: 'bold',
					color: '#b38e5d'
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.agreement-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}

	.agreement-content {
		padding: 20rpx;
	}

	.agreement-header {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 16rpx;
		margin-bottom: 20rpx;
		text-align: center;

		.agreement-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #303133;
			display: block;
			margin-bottom: 10rpx;
		}

		.update-time {
			font-size: 24rpx;
			color: #909399;
		}
	}

	.agreement-body {
		background-color: #fff;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;

		.section {
			margin-bottom: 40rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.section-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #303133;
				display: block;
				margin-bottom: 20rpx;
				padding-bottom: 10rpx;
				border-bottom: 2rpx solid #f0f0f0;
			}

			.section-content {
				font-size: 28rpx;
				color: #606266;
				line-height: 1.8;
				display: block;
				text-align: justify;
			}
		}
	}

	.agreement-footer {
		background-color: #fff;
		padding: 30rpx;
		border-radius: 16rpx;
		text-align: center;

		.footer-text {
			font-size: 30rpx;
			font-weight: bold;
			color: #b38e5d;
			display: block;
			margin-bottom: 10rpx;
		}

		.footer-date {
			font-size: 24rpx;
			color: #909399;
		}
	}
</style>
