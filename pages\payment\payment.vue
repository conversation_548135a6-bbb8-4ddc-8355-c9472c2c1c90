<template>
	<view class="page-container">
		<view class="tabs-container">
			<view class="tab-item" :class="{ 'active': currentTab === 0 }" @click="switchTab(0)">
				充值余额
			</view>
			<view class="tab-item" :class="{ 'active': currentTab === 1 }" @click="switchTab(1)">
				充值订单
			</view>
		</view>

		<view v-if="currentTab === 0" class="recharge-content">
			<view class="content-wrapper">
				<view class="step-section">
					<view class="section-title">
						<view class="title-decorator"></view>
						<text>步骤①</text>
					</view>
					<text class="step-description">
						请复制下方收款账号，进入支付宝手动粘贴转账，完成后把付款截图上传（必须显示订单号才有效）
					</text>
					
					<view class="uploader-container">
						<uni-file-picker 
							v-model="imageValue" 
							fileMediatype="image" 
							mode="grid" 
							@select="onFileSelect" 
							:limit="1"
							:auto-upload="false"
							title="付款截图">
						</uni-file-picker>
					</view>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="label">充值金额</text>
						<input class="input" type="digit" v-model="rechargeAmount" placeholder="请输入金额" />
					</view>
					<view class="form-item">
						<text class="label">付款单号</text>
						<input class="input" type="text" v-model="paymentSlipId" placeholder="请输入付款单号" />
					</view>
				</view>
	
				<view class="warning-box red-box">
					<uni-icons type="clear" size="18" color="#dd524d" class="icon"></uni-icons>
					<text>为保障您的资金安全，请使用支付宝或微信转账，非本人转账将记为资金报警备查，感谢支持与理解。</text>
				</view>
				
				<view class="instruction-text">
					请把充值金额转账到以下账户，转账成功后【截图】并【上传】转账凭证，支付宝将自动识别您的转账金额及订单号，如有疑问请联系客服处理，谢谢您的支持与理解
				</view>
	
				<view class="step-section">
					<view class="section-title">
						<view class="title-decorator"></view>
						<text>步骤②，请选择支付方式</text>
					</view>
				</view>
				
				<view class="payment-methods">
					<uni-collapse v-model="activeAccordion" accordion>
						<uni-collapse-item title="银行卡" name="bank">
							<view class="payment-list">
								<view class="payment-item" v-for="(account, index) in bankAccounts" :key="index">
									<view class="info-row">
										<text class="info-label">开户行：</text>
										<text class="info-value">{{ account.bankName }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.bankName)">复制</button>
									</view>
									<view class="info-row">
										<text class="info-label">支行：</text>
										<text class="info-value">{{ account.branchName }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.branchName)">复制</button>
									</view>
									<view class="info-row">
										<text class="info-label">收款人：</text>
										<text class="info-value">{{ account.payeeName }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.payeeName)">复制</button>
									</view>
									<view class="info-row">
										<text class="info-label">收款账户：</text>
										<text class="info-value">{{ account.payeeAccount }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.payeeAccount)">复制</button>
									</view>
								</view>
							</view>
						</uni-collapse-item>
						
						<uni-collapse-item title="支付宝" name="alipay">
							<view class="payment-list">
								<view class="payment-item" v-for="(account, index) in alipayAccounts" :key="index">
									<view class="info-row">
										<text class="info-label">收款人：</text>
										<text class="info-value">{{ account.payeeName }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.payeeName)">复制</button>
									</view>
									<view class="info-row">
										<text class="info-label">收款账号：</text>
										<text class="info-value">{{ account.payeeAccount }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.payeeAccount)">复制</button>
									</view>
								</view>
							</view>
						</uni-collapse-item>
						
						<uni-collapse-item title="微信" name="wechat">
							<view class="payment-list">
								<view class="payment-item" v-for="(account, index) in wechatAccounts" :key="index">
									<view class="info-row">
										<text class="info-label">收款人：</text>
										<text class="info-value">{{ account.payeeName }}</text>
										<button class="copy-btn" size="mini" @click="copyText(account.payeeName)">复制</button>
									</view>
									<view class="info-row" v-if="account.paymentDescription">
										<text class="info-label">收款说明：</text>
										<text class="info-value description">{{ account.paymentDescription }}</text>
									</view>
									<view class="qr-code-section" v-if="account.qrCodeUrl">
										<text class="info-label">收款二维码：</text>
										<image class="qr-code-image" :src="getFullImageUrl(account.qrCodeUrl)" mode="aspectFit" @click="previewImage(getFullImageUrl(account.qrCodeUrl))"></image>
									</view>
								</view>
							</view>
						</uni-collapse-item>
						
						<uni-collapse-item title="信用卡(千分之六手续费)" name="creditcard">
							<view class="payment-list">
								<view class="payment-item" v-for="(account, index) in creditCardAccounts" :key="index">
									<view class="info-row">
										<text class="info-label">收款人：</text>
										<text class="info-value">{{ account.payeeName }}</text>
									</view>
									<view class="info-row">
										<text class="info-label">说明：</text>
										<text class="info-value description">{{ account.payeeAccount }}</text>
									</view>
									<view class="qr-code-section" v-if="account.qrCodeUrl">
										<text class="info-label">收款二维码：</text>
										<image class="qr-code-image" :src="getFullImageUrl(account.qrCodeUrl)" mode="aspectFit" @click="previewImage(getFullImageUrl(account.qrCodeUrl))"></image>
									</view>
								</view>
							</view>
						</uni-collapse-item>
					</uni-collapse>
				</view>
			</view>
	
			<view class="footer">
				<button class="submit-btn" @click="submitRecharge">提交</button>
			</view>
		</view>

		<view v-if="currentTab === 1" class="order-list-container">
			<view v-if="orderListLoading" class="loading-state">
				<uni-load-more status="loading"></uni-load-more>
			</view>
			
			<view v-else-if="rechargeOrderList.length > 0" class="order-list">
				<view class="order-item" v-for="order in rechargeOrderList" :key="order.id">
					<view class="order-header">
						<text class="order-amount">充值金额：¥ {{ order.amount.toFixed(2) }}</text>
						<text class="order-status" :style="{ color: order.statusColor }">
							{{ order.statusText }}
						</text>
					</view>
					<view class="order-body">
						<view class="order-info-row">
							<text class="info-label">付款单号:</text>
							<text class="info-value">{{ order.paymentSlipId || '未填写' }}</text>
						</view>
						<view class="order-info-row">
							<text class="info-label">申请时间:</text>
							<text class="info-value">{{ order.createTime }}</text>
						</view>
						<view class="order-info-row" v-if="order.remark">
							<text class="info-label">审核备注:</text>
							<text class="info-value remark">{{ order.remark }}</text>
						</view>
					</view>
				</view>
			</view>
			
			<view v-else class="empty-state">
				<text>暂无充值订单</text>
			</view>
		</view>
	</view>
</template>

<script>
	import { listPaymentMethod } from "@/api/gold/paymentMethod.js"; 
	import { addRechargeOrder, listRechargeOrder } from "@/api/gold/rechargeOrder.js";
	import { getToken } from '@/utils/auth';

	export default {
		data() {
			return {
				currentTab: 0, 
				imageValue: [], 
				rechargeAmount: '',
				paymentSlipId: '',
				activeAccordion: '',
				bankAccounts: [],
				alipayAccounts: [],
				wechatAccounts: [],
				creditCardAccounts: [],
				baseUrl: process.env.VUE_APP_BASE_API || 'http://localhost:8080',
				userId:this.$store.state.user.userId,
				rechargeOrderList: [],
				orderListLoading: false,
				statusMap: {
					pending: { text: '待审核', color: '#ff9900' },
					approved: { text: '已通过', color: '#18bc37' },
					rejected: { text: '已驳回', color: '#dd524d' }
				}
			}
		},
		onLoad() {
			this.getPaymentMethod();
		},
		methods: {
			getPaymentMethod(){
				listPaymentMethod({ pageSize:10000, pageNum: 1 }).then(res => {
					if (res.code === 200 && res.rows) {
						this.bankAccounts = [];
						this.alipayAccounts = [];
						this.wechatAccounts = [];
						this.creditCardAccounts = [];
						res.rows.forEach(item => {
							switch(item.paymentType) {
								case 'bank_card': this.bankAccounts.push(item); break;
								case 'alipay': this.alipayAccounts.push(item); break;
								case 'wechat': this.wechatAccounts.push(item); break;
								case 'credit_card': this.creditCardAccounts.push(item); break;
							}
						});
						
						if (this.bankAccounts.length > 0) this.activeAccordion = 'bank';
						else if (this.alipayAccounts.length > 0) this.activeAccordion = 'alipay';
						else if (this.wechatAccounts.length > 0) this.activeAccordion = 'wechat';
						else if (this.creditCardAccounts.length > 0) this.activeAccordion = 'creditcard';
					}
				});
			},

			// 【修复】获取并预处理充值订单列表
			getRechargeOrders() {
				this.orderListLoading = true;
				listRechargeOrder({ pageSize: 20, pageNum: 1, userId:this.userId })
					.then(res => {
						if (res.code === 200) {
							// 在这里对数据进行预处理
							this.rechargeOrderList = res.rows.map(order => {
								const statusInfo = this.statusMap[order.status] || { text: '未知状态', color: '#999' };
								return {
									...order,
									statusText: statusInfo.text,
									statusColor: statusInfo.color
								};
							});
						}
					})
					.finally(() => {
						this.orderListLoading = false;
					});
			},
			
			getFullImageUrl(relativePath) {
				if (!relativePath || relativePath.startsWith('http')) return relativePath;
				return this.baseUrl + relativePath;
			},
			
			previewImage(url) {
				uni.previewImage({ urls: [url], current: url });
			},
			
			switchTab(index) {
				this.currentTab = index;
				if (index === 1) {
					this.getRechargeOrders();
				}
			},
			
			onFileSelect(e){
				this.imageValue = e.tempFiles; 
			},
			
			copyText(text) {
				if (!text) return uni.showToast({ title: '没有内容可复制', icon: 'none' });
				uni.setClipboardData({
					data: text,
					success: () => uni.showToast({ title: '复制成功', icon: 'success' })
				});
			},
			
			submitRecharge() {
				if (!this.rechargeAmount || this.rechargeAmount <= 0) {
					uni.showToast({ title: '请输入正确的充值金额', icon: 'none' });
					return;
				}
				if (this.imageValue.length === 0) {
					uni.showToast({ title: '请上传付款截图', icon: 'none' });
					return;
				}
				
				uni.showLoading({ title: '正在提交...' });
				
				const uploadUrl = this.baseUrl + '/common/upload'; 
				uni.uploadFile({
					url: uploadUrl,
					filePath: this.imageValue[0].path,
					name: 'file',
					header: {
						'Authorization': 'Bearer ' + getToken()
					},
					success: (uploadRes) => {
						const resData = JSON.parse(uploadRes.data);
						if (resData.code === 200) {
							const formData = {
								amount: this.rechargeAmount,
								paymentSlipId: this.paymentSlipId,
								screenshotUrl: resData.url
							};
							
							addRechargeOrder(formData).then(orderRes => {
								uni.hideLoading();
								if (orderRes.code === 200) {
									uni.showToast({ title: '提交成功，请等待审核', icon: 'success' });
									this.rechargeAmount = '';
									this.paymentSlipId = '';
									this.imageValue = [];
									this.switchTab(1);
								} else {
									uni.showToast({ title: orderRes.msg || '提交失败', icon: 'none' });
								}
							});
						} else {
							uni.hideLoading();
							uni.showToast({ title: resData.msg || '图片上传失败', icon: 'none' });
						}
					},
					fail: (err) => {
						uni.hideLoading();
						uni.showToast({ title: '图片上传请求失败', icon: 'none' });
						console.error('Upload fail:', err);
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
// 页面整体背景
.page-container {
	background-color: #f5f5f5;
	min-height: 100vh;
}

// Tabs
.tabs-container {
	display: flex;
	background-color: #ffffff;
	border-bottom: 1rpx solid #f0f0f0;
	
	.tab-item {
		flex: 1;
		text-align: center;
		padding: 25rpx 0;
		font-size: 30rpx;
		color: #666;
		position: relative;
		
		&.active {
			color: #dd524d;
			font-weight: bold;
			
			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 60rpx;
				height: 6rpx;
				background-color: #dd524d;
				border-radius: 3rpx;
			}
		}
	}
}

// 充值页面
.recharge-content {
	padding-bottom: 180rpx; /* 为底部按钮留出空间 */
}

// 主内容包裹器
.content-wrapper {
	padding: 20rpx;
	background-color: #ffffff;

	.warning-box {
		display: flex;
		align-items: center;
		padding: 20rpx;
		border-radius: 10rpx;
		font-size: 24rpx;
		margin-bottom: 30rpx;

		.icon {
			margin-right: 10rpx;
		}
		
		&.red-box {
			background-color: #fff0f0;
			border: 1rpx solid #ffd0d0;
			color: #dd524d;
		}
	}

	.step-section {
		margin-bottom: 30rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 15rpx;
			font-size: 32rpx;
			font-weight: bold;

			.title-decorator {
				width: 8rpx;
				height: 32rpx;
				background-color: #dd524d;
				margin-right: 15rpx;
				border-radius: 4rpx;
			}
		}

		.step-description {
			font-size: 28rpx;
			color: #dd524d;
			line-height: 1.6;
			text-align: center;
			display: block;
			padding: 10rpx 0;
		}
	}
	
	.uploader-container {
		margin-top: 30rpx;
		display: flex;
		justify-content: center;

		:v-deep .uni-file-picker__container {
			justify-content: center;
		}
		:v-deep .file-picker__box {
			width: 300rpx;
			height: 300rpx;
		}
	}
	
	.form-section {
		margin-bottom: 30rpx;

		.form-item {
			display: flex;
			align-items: center;
			border-bottom: 1rpx solid #f0f0f0;
			padding: 25rpx 0;

			.label {
				width: 160rpx;
				font-size: 28rpx;
				font-weight: bold;
			}
			.input {
				flex: 1;
				font-size: 28rpx;
			}
		}
	}

	.instruction-text {
		font-size: 26rpx;
		color: #333;
		line-height: 1.7;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
		margin-bottom: 30rpx;
	}

	.payment-methods {
		:v-deep .uni-collapse-item__title-text {
			font-size: 30rpx;
			font-weight: 500;
		}

		.payment-list {
			padding: 0 20rpx;
		}
		.payment-item {
			border: 1rpx solid #e0e0e0;
			border-radius: 10rpx;
			padding: 20rpx;
			margin-bottom: 20rpx;
		}
		.info-row {
			display: flex;
			align-items: center;
			font-size: 26rpx;
			margin-bottom: 15rpx;
			line-height: 1.5;

			&:last-child {
				margin-bottom: 0;
			}

			.info-label {
				color: #666;
				width: 150rpx;
				flex-shrink: 0;
			}
			.info-value {
				flex: 1;
				color: #333;
				font-weight: bold;
				word-break: break-all;
				
				&.description {
					font-weight: normal;
					color: #dd524d;
				}
			}
			.copy-btn {
				margin: 0;
				padding: 0 20rpx;
				font-size: 24rpx;
				background-color: #f0f0f0;
				border-color: #e0e0e0;
				color: #333;
				margin-left: 20rpx;
			}
		}
		.qr-code-section {
			display: flex;
			align-items: flex-start;
			.info-label {
				color: #666;
				width: 150rpx;
				flex-shrink: 0;
				padding-top: 10rpx;
			}
			.qr-code-image {
				width: 250rpx;
				height: 250rpx;
				border: 1px solid #eee;
			}
		}
	}
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	width: 100%;
	background-color: #ffffff;
	padding: 20rpx;
	box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
	box-sizing: border-box;

	.submit-btn {
		background-color: #1AAD19;
		color: #ffffff;
		border-radius: 50rpx;
		font-size: 32rpx;

		&:active {
			background-color: #128A12;
		}
	}
}

.order-list-container {
	padding: 20rpx;
	
	.loading-state {
		padding-top: 100rpx;
	}

	.order-item {
		background-color: #ffffff;
		border-radius: 16rpx;
		padding: 25rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);

		.order-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			border-bottom: 1rpx solid #f0f0f0;
			padding-bottom: 20rpx;
			margin-bottom: 20rpx;

			.order-amount {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}
			.order-status {
				font-size: 28rpx;
				font-weight: 500;
			}
		}

		.order-body {
			.order-info-row {
				display: flex;
				font-size: 26rpx;
				margin-bottom: 10rpx;
				color: #666;

				.info-label {
					width: 150rpx;
					flex-shrink: 0;
				}
				.info-value {
					color: #333;
					&.remark {
						color: #dd524d;
					}
				}
			}
		}
	}
	
	.empty-state {
		padding-top: 200rpx;
		text-align: center;
		font-size: 28rpx;
		color: #999;
	}
}
</style>