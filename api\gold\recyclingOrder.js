import request from '@/utils/request'

// 查询黄金白银回收订单列表
export function listRecyclingOrder(query) {
  return request({
    url: '/gold/recyclingOrder/list',
    method: 'get',
    params: query
  })
}

// 查询黄金白银回收订单详细
export function getRecyclingOrder(id) {
  return request({
    url: '/gold/recyclingOrder/' + id,
    method: 'get'
  })
}

// 新增黄金白银回收订单
export function addRecyclingOrder(data) {
  return request({
    url: '/gold/recyclingOrder',
    method: 'post',
    data: data
  })
}

// 修改黄金白银回收订单
export function updateRecyclingOrder(data) {
  return request({
    url: '/gold/recyclingOrder',
    method: 'put',
    data: data
  })
}

// 删除黄金白银回收订单
export function delRecyclingOrder(id) {
  return request({
    url: '/gold/recyclingOrder/' + id,
    method: 'delete'
  })
}
