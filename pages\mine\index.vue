<template>
	<view class="mine-container" :style="{height: `${windowHeight}px`}">
		<view class="header-section">
			<view class="user-card">
				<view class="user-avatar-section">
					<view v-if="!avatar" class="avatar-placeholder" @click="handleToLogin">
						<text class="avatar-text">👤</text>
					</view>
					<image v-if="avatar" @click="handleToAvatar" :src="avatar" class="user-avatar" mode="aspectFill">
					</image>
				</view>
				<view class="user-info-section">
					<view v-if="!name" @click="handleToLogin" class="login-tip">
						点击登录
					</view>
					<view v-if="name" @click="handleToInfo" class="user-info">
						<view class="user-name">{{ name }}</view>
						<view class="user-level">黄金会员</view>
					</view>
				</view>
				<view @click="handleToInfo" class="info-btn">
					<text class="arrow-text">›</text>
				</view>

				<!-- 装饰圆弧 -->
				<view class="deco-lines">
					<view v-for="i in 6" :key="i" :class="['deco-line', 'line-' + i]"></view>
				</view>
			</view>
		</view>

		<view class="content-section">
			<view class="order-management-card">
				<view class="card-header">
					<view class="card-title">
						<text class="list-icon">📋</text>
						<text>我的订单</text>
					</view>
					<view class="card-extra" @click="handleToAllOrders">
						<text>查看全部</text>
						<text class="arrow-small">›</text>
					</view>
				</view>
				<view class="actions-grid">
					<view class="action-item" v-for="(item, index) in orderActions" :key="index" @click="handleNavigate(item.path)">
						<view class="action-icon-wrapper">
							<text class="action-emoji">{{ item.emoji }}</text>
						</view>
						<text class="action-text">{{ item.text }}</text>
					</view>
				</view>
			</view>

			<view class="menu-list">
				<view class="menu-item" @click="handleToEditInfo">
					<view class="menu-item-content">
						<view class="menu-icon-wrapper">
							<text class="menu-emoji">✏️</text>
						</view>
						<text class="menu-text">编辑资料</text>
					</view>
					<text class="arrow-small">›</text>
				</view>
				<view class="menu-item" @click="handleLogout">
					<view class="menu-item-content">
						<view class="menu-icon-wrapper">
							<text class="menu-emoji">🚪</text>
						</view>
						<text class="menu-text">退出登录</text>
					</view>
					<text class="arrow-small">›</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import storage from '@/utils/storage'
	
	export default {
		data() {
			return {
				name: this.$store.state.user.name,
				orderActions: [
					{ text: '回收订单', emoji: '♻️', color: '#d4af37', path: '' },
					{ text: '板料订单', emoji: '🛒', color: '#b8860b', path: '' },
					{ text: '购物订单', emoji: '🛍️', color: '#daa520', path: '' },
					{ text: '物流记录', emoji: '🚚', color: '#cd853f', path: '' }
				]
			}
		},
		computed: {
			avatar() {
				return this.$store.state.user.avatar
			},
			windowHeight() {
				return uni.getSystemInfoSync().windowHeight
			}
		},
		methods: {
			handleToInfo() {
				this.$tab.navigateTo('/pages/mine/info/index')
			},
			handleToEditInfo() {
				this.$tab.navigateTo('/pages/mine/info/edit')
			},
			handleToLogin() {
				this.$tab.reLaunch('/pages/login')
			},
			handleToAvatar() {
				this.$tab.navigateTo('/pages/mine/avatar/index')
			},
			handleLogout() {
				this.$modal.confirm('确定注销并退出系统吗？').then(() => {
					this.$store.dispatch('LogOut').then(() => {
						this.$tab.reLaunch('/pages/index')
					})
				})
			},
			handleNavigate(path) {
				if (!path) {
					this.$modal.msg('功能开发中');
					return;
				}
				this.$tab.navigateTo(path);
			},
			handleToAllOrders() {
				this.handleNavigate('/pages/order/all');
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(135deg, #f8f4e6 0%, #fff 100%);
	}

	.mine-container {
		width: 100%;
		height: 100%;

		.header-section {
			padding: 80rpx 30rpx 80rpx;
			background: linear-gradient(135deg, #f4e4bc 0%, #e8d5a3 50%, #dcc688 100%);
			position: relative;
			overflow: hidden;

			.user-card {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 20rpx;
				padding: 30rpx;
				display: flex;
				align-items: center;
				box-shadow: 0 8rpx 32rpx rgba(179, 142, 93, 0.15);
				position: relative;
				overflow: hidden;

				.user-avatar-section {
					margin-right: 24rpx;

					.avatar-placeholder {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						background: linear-gradient(135deg, #f8f4e6, #e8d5a3);
						display: flex;
						align-items: center;
						justify-content: center;
						border: 3rpx solid #b38e5d;

						.avatar-text {
							font-size: 48rpx;
							color: #b38e5d;
						}
					}

					.user-avatar {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						border: 3rpx solid #b38e5d;
					}
				}

				.user-info-section {
					flex: 1;

					.login-tip {
						font-size: 32rpx;
						color: #b38e5d;
						font-weight: 500;
					}

					.user-info {
						.user-name {
							font-size: 32rpx;
							font-weight: bold;
							color: #303133;
							margin-bottom: 8rpx;
						}

						.user-level {
							font-size: 24rpx;
							color: #b38e5d;
							background: linear-gradient(90deg, #f8f4e6, #e8d5a3);
							padding: 4rpx 12rpx;
							border-radius: 12rpx;
							display: inline-block;
						}
					}
				}

				.info-btn {
					padding: 16rpx;

					.arrow-text {
						font-size: 32rpx;
						color: #b38e5d;
						font-weight: bold;
					}
				}

				// 装饰圆弧
				.deco-lines {
					position: absolute;
					top: 0;
					right: 0;
					width: 100%;
					height: 100%;
					pointer-events: none;
				}

				.deco-line {
					position: absolute;
					display: block;
					border-style: solid;
					border-width: 2rpx;
					border-color: transparent;
					border-radius: 50%;
					top: 50%;
					right: 40rpx;
					transform: translateY(-50%);
					border-top-color: rgba(179, 142, 93, 0.1);
					border-left-color: rgba(179, 142, 93, 0.1);

					@for $i from 1 through 6 {
						&.line-#{$i} {
							$size: 60rpx + ($i - 1) * 30rpx;
							width: $size;
							height: $size;
							opacity: 0.8 - ($i * 0.1);
						}
					}
				}
			}
		}

		.content-section {
			position: relative;
			top: -40rpx;

			.order-management-card {
				margin: 0 30rpx 30rpx;
				padding: 30rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.95);
				box-shadow: 0 8rpx 32rpx rgba(179, 142, 93, 0.1);
				backdrop-filter: blur(10rpx);

				.card-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					margin-bottom: 30rpx;
					padding-bottom: 20rpx;
					border-bottom: 2rpx solid rgba(179, 142, 93, 0.1);

					.card-title {
						display: flex;
						align-items: center;
						gap: 12rpx;
						font-size: 32rpx;
						font-weight: bold;
						color: #303133;

						.list-icon {
							font-size: 28rpx;
						}
					}

					.card-extra {
						display: flex;
						align-items: center;
						gap: 8rpx;
						font-size: 26rpx;
						color: #b38e5d;

						.arrow-small {
							font-size: 24rpx;
							color: #b38e5d;
							font-weight: bold;
						}
					}
				}

				.actions-grid {
					display: flex;
					justify-content: space-around;
					align-items: center;

					.action-item {
						display: flex;
						flex-direction: column;
						align-items: center;
						gap: 16rpx;
						cursor: pointer;

						.action-icon-wrapper {
							width: 80rpx;
							height: 80rpx;
							border-radius: 50%;
							background: linear-gradient(135deg, #b38e5d, #d4af37);
							display: flex;
							align-items: center;
							justify-content: center;
							box-shadow: 0 4rpx 16rpx rgba(179, 142, 93, 0.3);
							transition: all 0.3s ease;

							&:active {
								transform: scale(0.95);
							}

							.action-emoji {
								font-size: 32rpx;
							}
						}

						.action-text {
							font-size: 24rpx;
							color: #303133;
							font-weight: 500;
						}
					}
				}
			}

			.menu-list {
				margin: 0 30rpx;
				border-radius: 20rpx;
				background: rgba(255, 255, 255, 0.95);
				box-shadow: 0 8rpx 32rpx rgba(179, 142, 93, 0.1);
				backdrop-filter: blur(10rpx);
				overflow: hidden;

				.menu-item {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 32rpx 30rpx;
					position: relative;
					transition: background-color 0.3s ease;

					&:not(:last-child) {
						border-bottom: 1rpx solid rgba(179, 142, 93, 0.1);
					}

					&:active {
						background-color: rgba(179, 142, 93, 0.05);
					}

					.menu-item-content {
						display: flex;
						align-items: center;
						flex: 1;

						.menu-icon-wrapper {
							width: 60rpx;
							height: 60rpx;
							border-radius: 50%;
							background: linear-gradient(135deg, rgba(179, 142, 93, 0.1), rgba(212, 175, 55, 0.1));
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 24rpx;

							.menu-emoji {
								font-size: 24rpx;
							}
						}

						.menu-text {
							font-size: 30rpx;
							color: #303133;
							font-weight: 500;
						}
					}

					.arrow-small {
						font-size: 24rpx;
						color: #c0c4cc;
						font-weight: bold;
					}
				}
			}
		}
	}
</style>