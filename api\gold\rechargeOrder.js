import request from '@/utils/request'

// 查询用户充值订单列表
export function listRechargeOrder(query) {
  return request({
    url: '/gold/rechargeOrder/list',
    method: 'get',
    params: query
  })
}

// 查询用户充值订单详细
export function getRechargeOrder(id) {
  return request({
    url: '/gold/rechargeOrder/' + id,
    method: 'get'
  })
}

// 新增用户充值订单
export function addRechargeOrder(data) {
  return request({
    url: '/gold/rechargeOrder',
    method: 'post',
    data: data
  })
}

// 修改用户充值订单
export function updateRechargeOrder(data) {
  return request({
    url: '/gold/rechargeOrder',
    method: 'put',
    data: data
  })
}

// 删除用户充值订单
export function delRechargeOrder(id) {
  return request({
    url: '/gold/rechargeOrder/' + id,
    method: 'delete'
  })
}
