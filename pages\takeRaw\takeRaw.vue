<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false" lineWidth="30"></u-tabs>
		
		<view v-show="currentTab === 0">
			<view class="subsection-wrapper">
				<u-subsection
					:list="withdrawalTypes"
					:current="currentTypeTab"
					@change="handleTypeChange"
					activeColor="#c8a77f"
				></u-subsection>
			</view>

			<view class="content-wrapper" v-if="currentTypeTab === 0">
				<view class="balance-bar">可用金条：{{ userInfo.goldBar || '0' }} 条</view>
				<view class="product-list">
					<view class="product-item" v-for="item in goldBarList" :key="item.id">
						<image class="product-image" :src="item.image" mode="aspectFill"></image>
						<view class="product-info">
							<view class="product-name">{{ item.name }}</view>
							<view class="product-spec">规格: {{ item.weight }}g / 纯度: {{ item.purity }}</view>
						</view>
						<view class="product-action">
							<u-button type="primary" shape="circle" size="small" text="选择" class="action-btn"></u-button>
						</view>
					</view>
				</view>
			</view>
			
			<view class="content-wrapper" v-if="currentTypeTab === 1">
				<view class="balance-bar">可用金料：<text class="balance-value">{{ userInfo.goldRaw || '0.00' }}</text> g</view>
				<view class="form-panel">
					<u-form-item label="输入提取克重(g)" prop="weight">
						<u--input v-model="form.materialWeight" placeholder="请输入提取克重" type="digit"></u--input>
					</u-form-item>
				</view>
			</view>
			
			<view class="content-wrapper" v-if="currentTypeTab === 2">
				<view class="form-panel">
					<u-form-item label="上传许愿图" prop="wishImage">
						<u-upload name="file" :maxCount="1"></u-upload>
					</u-form-item>
					<u-form-item label="期望克重(g)" prop="wishWeight">
						<u--input v-model="form.wishWeight" placeholder="请输入期望克重(选填)" type="digit"></u--input>
					</u-form-item>
					<u-form-item label="备注信息" prop="wishRemark">
						<u--textarea v-model="form.wishRemark" placeholder="可输入款式、尺寸等要求" count></u--textarea>
					</u-form-item>
					<u-button type="primary" shape="circle" class="submit-btn" text="提交许愿单"></u-button>
				</view>
			</view>

			<view class="delivery-wrapper" v-if="currentTypeTab === 0 || currentTypeTab === 1">
				<view class="panel-title">配送方式</view>
				<u-radio-group v-model="deliveryType" placement="column">
					<view class="delivery-option">
						<u-icon name="map" size="22" color="#606266"></u-icon>
						<view class="option-info">
							<text class="option-title">快递邮寄</text>
							<view class="address-section" @click="selectAddress">
								<text class="option-desc" v-if="selectedAddress">
									{{ selectedAddress.recipientName }} {{ selectedAddress.phoneNumber }}
									{{ selectedAddress.region }} {{ selectedAddress.detailAddress }}
								</text>
								<text class="option-desc no-address" v-else>请选择收货地址</text>
								<u-icon name="arrow-right" size="14" color="#909399"></u-icon>
							</view>
						</view>
						<u-radio name="express"></u-radio>
					</view>
					<view class="delivery-option">
						<u-icon name="home" size="22" color="#606266"></u-icon>
						<view class="option-info">
							<text class="option-title">到店自提</text>
							<text class="option-desc">门店地址：广东省深圳市罗湖区水贝... (查看地图)</text>
						</view>
						<u-radio name="pickup"></u-radio>
					</view>
				</u-radio-group>
				<u-button type="primary" shape="circle" class="submit-btn" text="确认提货"></u-button>
			</view>

		</view>

		<view class="history-list-wrapper" v-show="currentTab === 1">
			<view class="history-item">
				<view class="item-header">
					<text class="item-id">订单号: TH20250713001</text>
					<u-tag text="已发货" type="primary" size="mini" plain></u-tag>
				</view>
				<view class="item-body">
					<view class="info-row">
						<text class="info-label">提取内容:</text>
						<text class="info-value">经典投资金条 50g</text>
					</view>
					<view class="info-row">
						<text class="info-label">提交时间:</text>
						<text class="info-value">2025-07-13 10:30</text>
					</view>
					<view class="info-row tracking-row">
						<text class="info-label">快递单号:</text>
						<text class="info-value tracking-number">SF1234567890123 (顺丰速运)</text>
					</view>
				</view>
			</view>
			<view class="history-item">
				<view class="item-header">
					<text class="item-id">订单号: TH20250712005</text>
					<u-tag text="待自提" type="warning" size="mini"></u-tag>
				</view>
				<view class="item-body">
					<view class="info-row">
						<text class="info-label">提取内容:</text>
						<text class="info-value">金料 25.50g</text>
					</view>
					<view class="info-row">
						<text class="info-label">提交时间:</text>
						<text class="info-value">2025-07-12 15:00</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { getUser } from "@/api/system/user.js"
	import { getToken } from "../../utils/auth";
	import { listUserAddress } from "@/api/gold/userAddress.js"

	export default {
		data() {
			return {
				tabsList: [{ name: '我要提货' }, { name: '提货记录' }],
				currentTab: 0,
				withdrawalTypes: ['提金条', '提金料', '许愿单'],
				currentTypeTab: 0,
				deliveryType: 'express', // 默认配送方式
				form: {
					materialWeight: '',
					wishWeight: '',
					wishRemark: ''
				},
				goldBarList: [
					{ id: 1, name: '经典投资金条', weight: 50, purity: 'Au 999.9', image: '/static/images/gold-bar-2.png' },
					{ id: 2, name: '经典投资金条', weight: 100, purity: 'Au 999.9', image: '/static/images/gold-bar-2.png' },
				],
				// 用户信息
				userInfo: {
					goldBar: '0',
					goldRaw: '0.00'
				},
				// 地址相关
				selectedAddress: null, // 选中的地址
				addressList: [] // 地址列表
			};
		},
		onLoad() {
			this.getUserInfo(); // 获取用户信息
			this.getDefaultAddress(); // 获取默认地址
		},
		onShow() {
			// 从地址管理页面返回时，重新获取地址列表以确保数据最新
			this.getDefaultAddress();
		},
		onShow() {
			// 从地址管理页面返回时，检查是否有选中的地址
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			if (currentPage.options && currentPage.options.selectedAddressId) {
				const addressId = currentPage.options.selectedAddressId;
				const selectedAddr = this.addressList.find(addr => addr.id == addressId);
				if (selectedAddr) {
					this.selectedAddress = selectedAddr;
				}
			}
		},
		methods: {
			// 获取用户信息
			async getUserInfo() {
				try {
					const token = getToken();
					const userId = this.$store.state.user.userId;

					if (!token || !userId) {
						return;
					}

					const response = await getUser(userId);
					if (response.code === 200 && response.data) {
						this.userInfo = {
							goldBar: response.data.goldBar || '0',
							goldRaw: response.data.goldRaw || '0.00'
						};
					}
				} catch (error) {
					console.error('获取用户信息失败:', error);
				}
			},
			// 获取默认地址
			async getDefaultAddress() {
				try {
					const token = getToken();
					const userId = this.$store.state.user.userId;

					if (!token || !userId) {
						return;
					}

					// 传递userId参数
					const params = {
						userId: userId
					};

					const response = await listUserAddress(params);
					if (response.code === 200 && response.rows) {
						this.addressList = response.rows;
						// 查找默认地址
						const defaultAddress = this.addressList.find(addr => addr.isDefault === 'Y');
						if (defaultAddress) {
							this.selectedAddress = defaultAddress;
						}
					}
				} catch (error) {
					console.error('获取地址列表失败:', error);
				}
			},
			// 选择地址
			selectAddress() {
				// 跳转到地址管理页面
				uni.navigateTo({
					url: '/pages/addressList/addressList?from=takeRaw'
				});
			},
			handleTabChange(item) {
				this.currentTab = item.index;
			},
			handleTypeChange(index) {
				this.currentTypeTab = index;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}
	.panel-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #303133;
		margin-bottom: 30rpx;
	}
	.submit-btn {
		margin-top: 40rpx;
		background: linear-gradient(to right, #d4b58e, #c8a77f);
		border: none;
	}

	.subsection-wrapper {
		padding: 20rpx;
	}
	
	.content-wrapper, .delivery-wrapper {
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
		background-color: #fff;
		border-radius: 16rpx;
	}
	
	.balance-bar {
		background-color: #fcf8f2;
		color: #8f6f40;
		padding: 16rpx 24rpx;
		border-radius: 8rpx;
		margin-bottom: 30rpx;
		font-size: 28rpx;
		.balance-value {
			font-weight: bold;
			font-size: 32rpx;
		}
	}

	.product-list {
		.product-item {
			display: flex;
			align-items: center;
			&:not(:last-child){
				margin-bottom: 30rpx;
			}
			.product-image {
				width: 120rpx;
				height: 120rpx;
				border-radius: 8rpx;
				margin-right: 20rpx;
				flex-shrink: 0;
			}
			.product-info {
				flex: 1;
				.product-name {
					font-size: 30rpx;
					font-weight: 500;
					color: #303133;
				}
				.product-spec {
					font-size: 24rpx;
					color: #909399;
					margin-top: 8rpx;
				}
			}
			.product-action {
				.action-btn {
					background: #c8a77f;
				}
			}
		}
	}

	.delivery-wrapper {
		.delivery-option {
			display: flex;
			align-items: center;
			padding: 24rpx 0;
			&:first-child {
				border-bottom: 1px solid #f5f5f5;
			}
			.option-info {
				flex: 1;
				margin: 0 20rpx;
				.option-title {
					font-size: 30rpx;
					color: #303133;
					margin-bottom: 4rpx;
				}
				.option-desc {
					font-size: 24rpx;
					color: #909399;

					&.no-address {
						color: #f56c6c;
					}
				}

				.address-section {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-top: 8rpx;
					padding: 8rpx 0;
					cursor: pointer;

					.option-desc {
						flex: 1;
						margin-right: 16rpx;
						line-height: 1.4;
					}
				}
			}
		}
	}

	.history-list-wrapper {
		padding: 20rpx;
		.history-item {
			background-color: #fff;
			border-radius: 12rpx;
			padding: 24rpx;
			margin-bottom: 20rpx;
			.item-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding-bottom: 20rpx;
				border-bottom: 1px solid #f5f5f5;
				margin-bottom: 20rpx;
				.item-id {
					font-size: 26rpx;
					color: #909399;
				}
			}
			.item-body {
				.info-row {
					display: flex;
					justify-content: space-between;
					align-items: center;
					font-size: 28rpx;
					color: #303133;
					&:not(:last-child) {
						margin-bottom: 16rpx;
					}
					.info-label {
						color: #606266;
					}
					.info-value {
						font-weight: 500;
						text-align: right;
					}
					&.tracking-row {
						padding: 16rpx;
						background-color: #f5f5f5;
						border-radius: 8rpx;
						margin-top: 20rpx;
						.tracking-number {
							color: #3c9cff;
						}
					}
				}
			}
		}
	}
</style>