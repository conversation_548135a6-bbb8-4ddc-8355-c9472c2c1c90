import request from '@/utils/request'

// 查询黄金购买订单列表
export function listGoldOrder(query) {
  return request({
    url: '/gold/goldOrder/list',
    method: 'get',
    params: query
  })
}

// 查询黄金购买订单详细
export function getGoldOrder(id) {
  return request({
    url: '/gold/goldOrder/' + id,
    method: 'get'
  })
}

// 新增黄金购买订单
export function addGoldOrder(data) {
  return request({
    url: '/gold/goldOrder',
    method: 'post',
    data: data
  })
}

// 修改黄金购买订单
export function updateGoldOrder(data) {
  return request({
    url: '/gold/goldOrder',
    method: 'put',
    data: data
  })
}

// 删除黄金购买订单
export function delGoldOrder(id) {
  return request({
    url: '/gold/goldOrder/' + id,
    method: 'delete'
  })
}
