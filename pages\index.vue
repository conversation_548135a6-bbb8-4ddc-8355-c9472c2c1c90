<template>
	<view class="page-container">
		<u-navbar
			:placeholder="true"
			:fixed="true"
			:safeAreaInsetTop="true"
			:border="false"
			bgColor="#f8f4e6"
			title="御禧黄金"
			:titleStyle="titleStyle"
			:customStyle="navbarCustomStyle"
		>
			<template #left>
				<text class="navbar-left-text">融金通</text>
			</template>
		</u-navbar> <u-swiper
			:list="swiperList"
			keyName="image"
			:autoplay="true"
			circular
			height="350rpx"
			radius="0"
			img-mode="aspectFill"
			indicator
			indicatorMode="dot"
			indicator-active-color="#BC876A"
		></u-swiper>
		
		<view class="price-card-panel">
			<view class="price-item">
				<text class="price-label">黄金销售：</text>
				<text class="price-value sell-price" :class="{ 'price-animate': priceAnimateSale }">{{ goldPrice.sale }}</text>
			</view>
			<view class="price-item">
				<text class="price-label">黄金回购：</text>
				<text class="price-value buy-back-price" :class="{ 'price-animate': priceAnimateBuyback }">{{ goldPrice.buyback }}</text>
			</view>
		</view>

		<view class="notice-bar-panel">
			<text class="notice-text">{{ noticeText }}</text>
		</view>
		
		<view class="user-card">
			<view class="deco-lines">
				<view v-for="i in 8" :key="i" :class="['deco-line', 'line-' + i]"></view>
			</view>

			<view class="user-info-row user-name-row">
				<text class="nickname">{{ userInfo.nickName }}</text>
			</view>
			
			<view class="user-info-row balance-row">
				<view class="balance-info">
					<text class="asset-label">账户余额：</text>
					<view class="asset-value-wrapper">
						<text class="asset-value large">{{ userInfo.money || '0.00' }}</text>
						<text class="asset-unit"> 元</text>
					</view>
				</view>
				<button v-if="isLogin" class="recharge-btn" @click="recharge">余额充值</button>
			</view>
			
			<view class="user-info-row gold-assets-row">
				<view class="asset-item">
					<text class="asset-label">账户金料：</text>
					<view class="asset-value-wrapper">
						<text class="asset-value">{{ userInfo.goldRaw || '0.00' }}</text>
						<text class="asset-unit"> g</text>
					</view>
				</view>
				<view class="asset-item">
					<text class="asset-label">账户金条：</text>
					<view class="asset-value-wrapper">
						<text class="asset-value">{{ userInfo.goldBar || '0.00' }}</text>
						<text class="asset-unit"> g</text>
					</view>
				</view>
			</view>

			<u-line color="#e0e0e0" margin="20rpx 0"></u-line>
			
			<view class="action-buttons">
				<view class="button-item" @click="toRawBuy">
					<u-icon name="shopping-cart" color="#c8a77f" size="28"></u-icon>
					<text class="button-text">购买</text>
				</view>
				<view class="button-item" @click="toRecycling">
					<u-icon name="rmb-circle" color="#c8a77f" size="28"></u-icon>
					<text class="button-text">回收</text>
				</view>
				<view class="button-item" @click="toSaveRaw">
					<u-icon name="lock" color="#c8a77f" size="28"></u-icon>
					<text class="button-text">存料</text>
				</view>
				<view class="button-item" @click="toTakeRaw">
					<u-icon name="car" color="#c8a77f" size="28"></u-icon>
					<text class="button-text">提货</text>
				</view>
			</view>
		</view>

		<view class="seckill-card">
			<view class="seckill-header-bar">
				<text class="seckill-title-main">限时秒杀</text>
				<text class="seckill-title-sub">活动时间：仅剩3天</text>
			</view>
			<view class="seckill-content">
				<image class="seckill-image" src="/static/images/gold-bar-hongfu.png" mode="widthFix"></image>
				<view class="seckill-details">
					<text class="item-name">鸿福金条</text>
					<text class="item-spec">规格: Au9999</text>
					<text class="item-spec">克重: 100.00g</text>
					<text class="item-spec">工费: 5元/克</text>
					<text class="item-price">限时: 78500.00元</text>
				</view>
			</view>
		</view>

	</view>
</template>

<script>
	import { getUser } from "@/api/system/user.js"
	import { listSwiper } from "@/api/gold/swiper.js"
	import { listNotice } from "@/api/system/notice.js"
	import { baseUrl } from "@/config"

	export default {
		data() {
			return {
				userId: null,
				baseUrl,
				isLogin: false,
				swiperList: [],
				noticeText: '暂无公告',
				goldPrice: {
					sale: '774.50',
					buyback: '772.81'
				},
			priceTimer: null, // 价格刷新定时器
			priceAnimateSale: false, // 销售价格动画状态
			priceAnimateBuyback: false, // 回购价格动画状态
				userInfo: { nickName: '点击登录', money: '0.00', goldRaw: '0.00', goldBar: '0.00' },
				statusBarHeight: 0,
			// 导航栏样式配置
			titleStyle: {
				fontSize: '36rpx',
				fontWeight: 'bold',
				color: '#b38e5d',
				textShadow: '0 1rpx 2rpx rgba(179, 142, 93, 0.2)'
			},
			navbarCustomStyle: {
				background: 'linear-gradient(135deg, #f8f4e6 0%, #fff 100%)',
				boxShadow: '0 2rpx 8rpx rgba(0, 0, 0, 0.1)'
			},
			}
		},
		onShow() {
			this.checkLoginAndInit();
			// 页面显示时启动价格定时器
			this.getGoldPrice(); // 立即获取一次价格
			this.startPriceTimer(); // 启动定时器
		},
		onHide() {
			// 页面隐藏时清除定时器
			this.clearPriceTimer();
		},
		onLoad() {
			this.getSystemInfo();
			this.getSwiperData();
			this.getNoticeData();
			// 不在onLoad中启动定时器，改为在onShow中启动
		},
		onUnload() {
			// 页面卸载时清除定时器
			this.clearPriceTimer();
		},
		methods: {
			getSystemInfo() {
				this.statusBarHeight = uni.getSystemInfoSync().statusBarHeight;
			},
			// 获取黄金价格
			async getGoldPrice() {
				try {
					const response = await uni.request({
						url: 'https://goldapi.szzjtech.cn/jjgold',
						method: 'GET',
						timeout: 10000
					});

					// uni.request返回的是数组，第二个元素是实际响应数据
					const data = response[1];

					if (data && data.statusCode === 200 && data.data && data.data.data.items) {
						const items = data.data.data.items;

						if (items['1']) {
							// 黄金销售价格 (askprice) 和回购价格 (bidprice)
							const newSale = items['1'].askprice || '0.00';
							const newBuyback = items['1'].bidprice || '0.00';

							// 使用动画更新价格
							this.animatePrice('sale', newSale);
							this.animatePrice('buyback', newBuyback);
						}
					}
				} catch (error) {
					console.error('获取黄金价格失败:', error);
				}
			},
			// 价格动画更新
			animatePrice(type, newPrice) {
				if (this.goldPrice[type] !== newPrice) {
					// 更新价格值
					this.goldPrice[type] = newPrice;

					// 触发动画
					this.$nextTick(() => {
						const animateProperty = type === 'sale' ? 'priceAnimateSale' : 'priceAnimateBuyback';
						this[animateProperty] = true;

						// 动画结束后移除类
						setTimeout(() => {
							this[animateProperty] = false;
						}, 600);
					});
				}
			},
			// 启动价格刷新定时器
			startPriceTimer() {
				// 先清除已存在的定时器，避免重复创建
				this.clearPriceTimer();
				// 每5秒刷新一次价格
				this.priceTimer = setInterval(() => {
					this.getGoldPrice();
				}, 5000);
			},
			// 清除价格刷新定时器
			clearPriceTimer() {
				if (this.priceTimer) {
					clearInterval(this.priceTimer);
					this.priceTimer = null;
				}
			},
			checkLoginAndInit() {
				const token = this.$store.state.user.token;
				const userId = this.$store.state.user.userId;

				if (token && userId) {
					this.userId = userId;
					this.isLogin = true;
					getUser(this.userId).then(res => {
						if (res.code === 200) {
							this.userInfo = res.data;
						} else { this.clearLoginState(); }
					}).catch(() => { this.clearLoginState(); });
				} else {
					this.clearLoginState();
				}
			},
			clearLoginState() {
				this.isLogin = false;
				this.userId = null;
				this.userInfo = { nickName: '点击登录', money: '0.00', goldRaw: '0.00', goldBar: '0.00' };
			},
			getSwiperData() {
				listSwiper().then(res => {
					if (res.code === 200 && res.rows) {
						this.swiperList = res.rows.map(item => ({ image: this.baseUrl + item.pic }));
					}
				}).catch(()=>{
					this.swiperList = [
						{ image: '/static/images/pendant-1.png' },
						{ image: '/static/images/pendant-2.png' },
						{ image: '/static/images/pendant-3.png' }
					];
				});
			},
			getNoticeData() {
				listNotice({ noticeType: "1" }).then(res => {
					if (res.code === 200 && res.rows && res.rows.length > 0) {
						this.noticeText = res.rows[0].noticeTitle;
					} else {
						this.noticeText = '2025年5月31日端午节休市，6月3日恢复。'
					}
				});
			},
			recharge() { uni.navigateTo({ url:"/pages/payment/payment" }) },
			toRecycling(){ uni.navigateTo({ url:"/pages/recycling/recycling" }) },
			toRawBuy(){ uni.navigateTo({ url:"/pages/rawBuy/rawBuy" }) },
			toSaveRaw(){ uni.navigateTo({ url:"/pages/saveRaw/saveRaw" }) },
			toTakeRaw(){ uni.navigateTo({ url:"/pages/takeRaw/takeRaw" }) },
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #FFFFF3;
		min-height: 100vh;
		padding-bottom: 20rpx;
	}

	// 导航栏左侧文字样式
	.navbar-left-text {
		font-size: 26rpx;
		color: #8a7968;
		font-weight: 500;
	}

	.price-card-panel {
		background-color: #fff;
		margin: 20rpx;
		border-radius: 16rpx;
		box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.05);
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 40rpx;

		.price-item {
			display: flex; align-items: baseline;
			.price-label { font-size: 28rpx; color: #606266; }
			.price-value {
				font-size: 36rpx; font-weight: bold; margin-left: 10rpx;
				transition: all 0.3s ease;
				position: relative;

				&.sell-price { color: #fa3534; }
				&.buy-back-price { color: #19be6b; }

				// 价格变化动画
				&.price-animate {
					animation: priceChange 0.6s ease-in-out;
				}
			}
		}
	}

	// 价格变化动画关键帧
	@keyframes priceChange {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		25% {
			transform: scale(1.1);
			opacity: 0.8;
		}
		50% {
			transform: scale(1.15);
			opacity: 0.6;
			color: #f39c12 !important;
		}
		75% {
			transform: scale(1.1);
			opacity: 0.8;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.notice-bar-panel {
		background-color: #EB942D;
		padding: 12rpx 30rpx;
		text-align: center;
		margin: 20rpx 0;

		.notice-text { 
			font-size: 26rpx;
			color: #FFFFFF;
		}
	}

	.user-card {
		background-color: #FFEEDC;
		margin: 20rpx;
		padding: 30rpx; 
		border-radius: 16rpx;
		position: relative;
		overflow: hidden;

		.deco-lines {
			position: absolute;
			top: 0; left: 0;
			width: 100%; height: 100%;
			pointer-events: none;
			z-index: 0;
			overflow: hidden;

			.deco-line {
				position: absolute;
				display: block;
				border-style: solid;
				border-width: 4px; // 加粗线条，让圆弧更明显
				border-color: transparent;
				border-radius: 50%;
				top: 50%;
				right: 60rpx; // 调整圆心位置，让最大圆弧在充值按钮左边一点点
				transform: translateY(-50%); // 只垂直居中
				// 只显示四分之一圆弧（左上角弧线）
				border-top-color: rgba(224, 184, 142, 0.6); // 提高透明度，让圆弧更明显
				border-left-color: rgba(224, 184, 142, 0.6); // 提高透明度，让圆弧更明显

				// 使用伪元素遮挡右下部分，只显示左上角四分之一
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 0;
					width: 100%;
					height: 50%;
					background-color: #FFEEDC;
					z-index: 1;
				}

				&::before {
					content: '';
					position: absolute;
					top: 0;
					left: 50%;
					width: 50%;
					height: 100%;
					background-color: #FFEEDC;
					z-index: 1;
				}
			}

			@for $i from 1 through 8 {
				.line-#{$i} {
					$size: 80rpx + ($i - 1) * 60rpx; // 调整起始大小和间距
					width: $size;
					height: $size;
					opacity: 0.9 - ($i * 0.1); // 调整透明度渐变
				}
			}
		}
		
		.user-info-row, .u-line, .action-buttons {
			position: relative;
			z-index: 1;
		}

		.user-info-row {
			display: flex;
			align-items: center;
			&:not(:last-child){
				margin-bottom: 20rpx;
			}
		}
		
		.user-name-row {
			.nickname {
				font-size: 44rpx; font-weight: bold; color: #303133;
				font-family: 'KaiTi', 'STKaiti', serif;
			}
		}

		.balance-row {
			justify-content: space-between;
			.balance-info {
				display: flex;
				align-items: baseline;
				.asset-label { font-size: 26rpx; color: #909399; margin-right: 10rpx; }
			}
			.recharge-btn {
				background-color: #FA171E; color: #fff; font-size: 24rpx;
				padding: 0 24rpx; margin: 0; line-height: 52rpx; height: 52rpx;
				border-radius: 50rpx;
				&::after { border: none; }
			}
		}

		.gold-assets-row {
			justify-content: flex-start;
			gap: 40rpx;
			.asset-item {
				display: flex;
				align-items: baseline;
				.asset-label { font-size: 26rpx; color: #909399; }
			}
		}
		
		.asset-value-wrapper {
			display: flex;
			align-items: baseline;
			.asset-value {
				font-weight: bold;
				color: #fa3534;
				font-size: 28rpx;
				&.large {
					font-size: 36rpx;
				}
			}
			.asset-unit {
				font-size: 24rpx;
				color: #303133;
				margin-left: 4rpx;
			}
		}

		.u-line {
			margin: 20rpx 0 !important;
		}
		.action-buttons {
			display: flex; justify-content: space-around; align-items: center; padding-top: 20rpx;
			.button-item {
				display: flex; flex-direction: column; align-items: center; gap: 10rpx;
				.button-text { font-size: 26rpx; color: #303133; }
			}
		}
	}
	
	.seckill-card {
		margin: 20rpx;
		.seckill-header-bar {
			display: flex; justify-content: space-between; align-items: center;
			background-color: #B00606; color: #fff;
			padding: 16rpx 24rpx; border-radius: 16rpx 16rpx 0 0;
			.seckill-title-main { font-size: 32rpx; font-weight: bold; }
			.seckill-title-sub { font-size: 24rpx; }
		}
		.seckill-content {
			display: flex; align-items: center; gap: 30rpx;
			background-color: #fcf8f2; padding: 30rpx;
			border-radius: 0 0 16rpx 16rpx;
			.seckill-image { width: 200rpx; height: 200rpx; border-radius: 8rpx; flex-shrink: 0; }
			.seckill-details {
				display: flex; flex-direction: column; font-size: 26rpx;
				color: #606266; line-height: 1.7;
				.item-name {
					font-size: 30rpx; font-weight: bold; color: #303133; margin-bottom: 5rpx;
				}
				.item-price {
					color: #fa3534; font-weight: bold; font-size: 32rpx; margin-top: 5rpx;
				}
			}
		}
	}
</style>