<template>
	<view class="page-container">
		<scroll-view class="left-panel" scroll-y="true">
			<view 
				class="product-item" 
				v-for="(item, index) in productList" 
				:key="item.id"
				:class="{ 'active': activeIndex === index }"
				@click="selectProduct(item, index)"
			>
				{{ item.name }}
			</view>
		</scroll-view>

		<scroll-view class="right-panel" scroll-y="true">
			<view class="product-details">
				<view class="image-wrapper">
					<image class="product-image" :src="currentProduct.image" mode="aspectFit"></image>
				</view>
				
				<view class="description">
					{{ currentProduct.description }}
					<view class="extra-info">
						工费{{ currentProduct.laborFee }}/g，工期{{ currentProduct.workDuration }}个工作日
					</view>
				</view>
				
				<view class="form-section">
					<u--form :model="form" labelPosition="left" labelWidth="90">
						<u-form-item label="克重" prop="weight">
							<u--input v-model="form.weight" placeholder="请输入28至32的整数" type="number" @input="updatePrice"></u--input>
						</u-form-item>
						<u-form-item label="圈口" prop="ringSize">
							<u--input v-model="form.ringSize" placeholder="请输入54至60的整数" type="number"></u--input>
						</u-form-item>
						<u-form-item label="做工/规格" prop="spec">
							<view class="spec-tags">
								<view 
									class="tag" 
									v-for="spec in specOptions" :key="spec"
									:class="{ 'active': form.spec === spec }"
									@click="selectSpec(spec)"
								>
									{{ spec }}
								</view>
							</view>
						</u-form-item>
					</u--form>
				</view>
				
				<view class="summary-display">
					<text class="summary-label">当前选择</text>
					<text class="summary-text">{{ summaryText }}</text>
				</view>
				
				<view class="user-info-section">
					<u-form-item label="微信昵称" labelWidth="90">
						<u--input v-model="form.nickname" placeholder="请输入昵称"></u--input>
					</u-form-item>
					<view class="price-display">
						<text class="price-label">定金价格</text>
						<view class="price-value">
							<text class="currency">¥</text>
							<text class="amount">{{ depositPrice }}</text>
						</view>
						<text class="price-note">(实时金价 ¥{{ realTimeGoldPrice }}*克重)</text>
					</view>
				</view>
				
				<view class="notice-box">
					<u-icon name="info-circle-fill" color="#fa3534" size="16"></u-icon>
					<text class="notice-text">提示:本价格仅为金价不包含工费,且由于金价波动频繁,提交订单后请5分钟内上传付款截图,否则本价格作废。</text>
				</view>
			</view>
		</scroll-view>

		<view class="footer-submit">
			<u-button type="primary" shape="circle" text="提交订单" class="submit-btn"></u-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				// 左侧产品列表
				productList: [
					{ id: 1, name: '金缕雅韵镯', image: '/static/images/product-1.png', description: '细腻花丝如流云缠绕，每一道纹路都藏着匠心温度。', laborFee: 18, workDuration: '3-7' },
					{ id: 2, name: '天地地方足金手镯', image: '/static/images/product-2.png', description: '天圆地方，方圆之间，尽显东方哲学之美。', laborFee: 20, workDuration: '5-8' },
					{ id: 3, name: '蟒蛇手镯', image: '/static/images/product-3.png', description: '灵蛇盘绕，寓意财富与智慧，设计大胆前卫。', laborFee: 22, workDuration: '4-6' },
					{ id: 4, name: '古法竹叶手镯', image: '/static/images/product-1.png', description: '竹叶青青，节节高升，承载着美好的祝福。', laborFee: 15, workDuration: '3-5' },
					{ id: 5, name: '浮云手镯', image: '/static/images/product-2.png', description: '设计轻盈飘逸，如天边浮云，自在洒脱。', laborFee: 16, workDuration: '3-6' },
				],
				activeIndex: 0, // 当前选中产品的索引
				currentProduct: {}, // 当前选中的产品对象
				// 右侧表单数据
				form: {
					weight: '',
					ringSize: '',
					spec: '倒模', // 默认选中
					nickname: ''
				},
				specOptions: ['倒模', '手工'],
				realTimeGoldPrice: 774.3,
				depositPrice: '0.00'
			};
		},
		computed: {
			// 动态生成当前选择的汇总文本
			summaryText() {
				const weight = this.form.weight ? `${this.form.weight}克` : '未输入';
				const ringSize = this.form.ringSize ? `${this.form.ringSize}圈` : '未输入';
				const spec = this.form.spec || '未选择';
				return `克重 ${weight} / 圈口 ${ringSize} / 做工 ${spec}`;
			}
		},
		created() {
			// 初始化时，默认选中第一个商品
			this.selectProduct(this.productList[0], 0);
		},
		methods: {
			// 选择左侧商品
			selectProduct(item, index) {
				this.activeIndex = index;
				this.currentProduct = item;
				// 可以在这里清空或重置右侧表单
				this.form.weight = '';
				this.form.ringSize = '';
				this.updatePrice();
			},
			// 选择做工/规格
			selectSpec(spec) {
				this.form.spec = spec;
			},
			// 更新价格
			updatePrice() {
				const weight = parseFloat(this.form.weight);
				if (isNaN(weight) || weight <= 0) {
					this.depositPrice = '0.00';
					return;
				}
				this.depositPrice = (this.realTimeGoldPrice * weight).toFixed(2);
			}
		}
	}
</script>

<style lang="scss" scoped>
	// 使用 page-container 伪元素撑开底部安全区和按钮区域
	.page-container::after {
		content: '';
		display: block;
		height: 140rpx;
	}
	.page-container {
		display: flex;
		height: 100vh;
		background-color: #f5f5f5;
		
		.left-panel {
			width: 200rpx;
			background-color: #f8f8f8;
			height: 100%;
			
			.product-item {
				padding: 30rpx 20rpx;
				font-size: 28rpx;
				color: #606266;
				text-align: center;
				position: relative;
				
				&.active {
					background-color: #fff;
					color: #303133;
					font-weight: bold;
					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 8rpx;
						height: 40rpx;
						background-color: #c8a77f;
					}
				}
			}
		}
		
		.right-panel {
			flex: 1;
			height: 100%;
			background-color: #fff;
			
			.product-details {
				padding: 30rpx;
			}
			
			.image-wrapper {
				text-align: center;
				margin-bottom: 30rpx;
				.product-image {
					width: 250rpx;
					height: 250rpx;
					border-radius: 16rpx;
				}
			}
			
			.description {
				font-size: 28rpx;
				color: #303133;
				line-height: 1.6;
				margin-bottom: 30rpx;
				.extra-info {
					font-size: 26rpx;
					color: #fa3534;
					margin-top: 10rpx;
				}
			}
			
			.form-section {
				.spec-tags {
					display: flex;
					gap: 20rpx;
					.tag {
						background-color: #f5f5f5;
						color: #606266;
						padding: 10rpx 24rpx;
						border-radius: 8rpx;
						font-size: 26rpx;
						&.active {
							background-color: #c8a77f;
							color: #fff;
						}
					}
				}
			}
			
			.summary-display {
				background-color: #f8f8f8;
				border-radius: 8rpx;
				padding: 20rpx;
				margin-top: 20rpx;
				font-size: 26rpx;
				.summary-label {
					color: #606266;
					margin-right: 20rpx;
				}
				.summary-text {
					color: #303133;
				}
			}

			.user-info-section {
				margin-top: 20rpx;
				.price-display {
					margin-top: 20rpx;
					text-align: right;
					.price-label {
						font-size: 28rpx;
						color: #303133;
						margin-right: 10rpx;
					}
					.price-value {
						display: inline-block;
						color: #fa3534;
						font-weight: bold;
						.currency {
							font-size: 30rpx;
						}
						.amount {
							font-size: 48rpx;
						}
					}
					.price-note {
						display: block;
						font-size: 24rpx;
						color: #909399;
						margin-top: 5rpx;
					}
				}
			}
			
			.notice-box {
				display: flex;
				align-items: flex-start;
				gap: 10rpx;
				border: 1px solid #fa3534;
				background-color: #fef0f0;
				color: #fa3534;
				padding: 20rpx;
				border-radius: 8rpx;
				margin-top: 40rpx;
				.notice-text {
					flex: 1;
					font-size: 24rpx;
					line-height: 1.5;
				}
			}
		}
	}
	
	.footer-submit {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #fff;
		padding: 20rpx 30rpx;
		padding-bottom: calc(20rpx + constant(safe-area-inset-bottom));
		padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
		border-top: 1px solid #f0f0f0;

		.submit-btn {
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}
</style>