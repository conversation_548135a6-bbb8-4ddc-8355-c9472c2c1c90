import request from '@/utils/request'

// 查询付款方式列表
export function listPaymentMethod(query) {
  return request({
    url: '/gold/paymentMethod/list',
    method: 'get',
    params: query
  })
}

// 查询付款方式详细
export function getPaymentMethod(id) {
  return request({
    url: '/gold/paymentMethod/' + id,
    method: 'get'
  })
}

// 新增付款方式
export function addPaymentMethod(data) {
  return request({
    url: '/gold/paymentMethod',
    method: 'post',
    data: data
  })
}

// 修改付款方式
export function updatePaymentMethod(data) {
  return request({
    url: '/gold/paymentMethod',
    method: 'put',
    data: data
  })
}

// 删除付款方式
export function delPaymentMethod(id) {
  return request({
    url: '/gold/paymentMethod/' + id,
    method: 'delete'
  })
}
