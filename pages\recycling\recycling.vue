<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false" lineWidth="30"></u-tabs>
		
		<view v-show="currentTab === 0">
			<view class="price-section">
				<view class="price-card gold" :class="{ 'selected': selectedMetal === 'gold' }" @click="selectMetal('gold')">
					<view class="price-title-row">
						<text class="price-title">实时金价 (黄金)</text>
						<text class="price-tag">实时变动</text>
					</view>
					<view class="price-value" :class="{ 'price-animate': goldPriceAnimate }">
						<text class="currency">¥</text>
						<text class="price-integer">{{ goldPriceInteger }}</text>
						<text class="price-decimal">.{{ goldPriceDecimal }} /g</text>
					</view>
					<view class="selection-indicator" v-if="selectedMetal === 'gold'">
						<text class="checkmark-text">✓</text>
					</view>
				</view>
				<view class="price-card silver" :class="{ 'selected': selectedMetal === 'silver' }" @click="selectMetal('silver')">
					<view class="price-title-row">
						<text class="price-title">实时银价 (白银)</text>
						<text class="price-tag">实时变动</text>
					</view>
					<view class="price-value" :class="{ 'price-animate': silverPriceAnimate }">
						<text class="currency">¥</text>
						<text class="price-integer">{{ silverPriceInteger }}</text>
						<text class="price-decimal">.{{ silverPriceDecimal }} /g</text>
					</view>
					<view class="selection-indicator" v-if="selectedMetal === 'silver'">
						<text class="checkmark-text">✓</text>
					</view>
				</view>
			</view>

			<view class="recycle-panel">
				<h3 class="panel-title">预约高价回收 ({{ selectedMetal === 'gold' ? '黄金' : '白银' }})</h3>
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<u-form-item label="预估回收克重(g)" prop="weight">
						<u--input v-model="form.weight" :placeholder="`请输入您预估的${selectedMetal === 'gold' ? '黄金' : '白银'}克重`" type="digit" @input="calculateDeposit"></u--input>
					</u-form-item>

					<u-form-item label="选择保价期限" prop="depositType" labelPosition="top">
						<u-radio-group v-model="form.depositType" @change="calculateDeposit" class="deposit-options">
							<view class="option-card" :class="{'active': form.depositType === '5'}" @click="selectDepositType('5')">
								<view class="option-title">5天内有效</view>
								<view class="option-desc">保证金 20元/克</view>
								<u-radio name="5" :customStyle="{position: 'absolute', top: '10rpx', right: '10rpx'}"></u-radio>
							</view>
							<view class="option-card" :class="{'active': form.depositType === '10'}" @click="selectDepositType('10')">
								<view class="option-title">10天内有效</view>
								<view class="option-desc">保证金 25元/克</view>
								<u-radio name="10" :customStyle="{position: 'absolute', top: '10rpx', right: '10rpx'}"></u-radio>
							</view>
						</u-radio-group>
					</u-form-item>

					<u-form-item label="预估保证金">
						<text class="deposit-amount">¥ {{ estimatedDeposit }}</text>
					</u-form-item>
				</u--form>

				<u-button type="primary" shape="circle" class="submit-btn" text="立即锁定价格" @click="lockPrice"></u-button>
			</view>

			<view class="process-panel">
				<h3 class="panel-title">回收流程</h3>
				<u-steps :current="0" activeColor="#c8a77f">
					<u-steps-item title="锁定价格"></u-steps-item>
					<u-steps-item title="邮寄黄金"></u-steps-item>
					<u-steps-item title="专业检测"></u-steps-item>
					<u-steps-item title="结算尾款"></u-steps-item>
				</u-steps>
			</view>
		</view>

		<view v-show="currentTab === 1">
			<view class="orders-panel">
				<view class="order-list">
					<view class="order-item" v-for="order in orderList" :key="order.id">
						<view class="order-header">
							<text class="order-id">订单号: {{ order.orderNumber }}</text>
							<u-tag :text="getStatusText(order.status)" :type="getStatusType(order.status)" size="mini"></u-tag>
						</view>
						<view class="order-body">
							<view class="order-desc">预约回收：{{ order.metalType === 'GOLD' ? '黄金' : '白银' }} 约 {{ order.declaredWeightGrams }} 克</view>
							<view class="order-price">锁定价格：¥{{ order.lockedPricePerGram }}/克</view>
							<view class="order-deposit">保证金：¥{{ order.totalDepositAmount }}</view>
							<view class="order-time">{{ formatTime(order.createTime) }}</view>
						</view>
						<view class="order-footer" v-if="order.status === 'PENDING_SHIPMENT'">
							<u-button shape="circle" size="small" text="提交快递单号" @click="submitTrackingNo(order.id)"></u-button>
						</view>
					</view>
					<u-empty v-if="!orderList.length && !loading" mode="order" text="暂无回收订单" marginTop="150"></u-empty>
					<u-loadmore v-if="orderList.length > 0" :status="loadmoreStatus" @loadmore="loadMore"></u-loadmore>
				</view>
			</view>
		</view>

		<!-- 锁定价格确认弹框 -->
		<view v-if="showLockPriceModal" class="custom-modal-overlay" @click="closeLockPriceModal">
			<view class="custom-modal" @click.stop>
				<view class="modal-header">
					<text class="modal-title">锁定价格确认</text>
				</view>
				<view class="modal-content">
					<view class="confirm-row">
						<text class="confirm-label">回收类型：</text>
						<text class="confirm-value">{{ selectedMetal === 'gold' ? '黄金' : '白银' }}</text>
					</view>
					<view class="confirm-row">
						<text class="confirm-label">预估克重：</text>
						<text class="confirm-value">{{ form.weight }}克</text>
					</view>
					<view class="confirm-row">
						<text class="confirm-label">锁定价格：</text>
						<text class="confirm-value">¥{{ currentPrice }}/克</text>
					</view>
					<view class="confirm-row">
						<text class="confirm-label">保价期限：</text>
						<text class="confirm-value">{{ form.depositType }}天内有效</text>
					</view>
					<view class="confirm-row">
						<text class="confirm-label">保证金费率：</text>
						<text class="confirm-value">{{ depositRate }}元/克</text>
					</view>
					<view class="confirm-row">
						<text class="confirm-label">保证金总额：</text>
						<text class="confirm-value">¥{{ estimatedDeposit }}</text>
					</view>
				</view>
				<view class="modal-footer">
					<view class="modal-btn cancel-btn" @click="closeLockPriceModal">取消</view>
					<view class="modal-btn confirm-btn" @click="confirmLockPrice">确认</view>
				</view>
			</view>
		</view>


	</view>
</template>

<script>
	import { addRecyclingOrder,listRecyclingOrder,updateRecyclingOrder } from "@/api/gold/recyclingOrder.js"
	export default {
		data() {
			return {
				tabsList: [{ name: '预约回收' }, { name: '订单管理' }],
				currentTab: 0,
				form: {
					weight: '',
					depositType: '5', // 默认选中5天
				},
				estimatedDeposit: '0.00',
				// 订单列表数据
				orderList: [],
				loading: false,
				loadmoreStatus: 'loadmore', // loadmore, loading, nomore
				pageNum: 1,
				pageSize: 10,
				total: 0,
				// 价格相关数据
				goldPrice: 545.60, // 黄金回购价格
				silverPrice: 6.88, // 白银回购价格
				priceTimer: null, // 价格刷新定时器
				goldPriceAnimate: false, // 黄金价格动画状态
				silverPriceAnimate: false, // 白银价格动画状态
				selectedMetal: 'gold', // 选中的金属类型：'gold' 或 'silver'
				userId: this.$store.state.user.userId,
				// 锁定价格弹框
				showLockPriceModal: false,
				submitting: false
			};
		},
		computed: {
			// 黄金价格整数部分
			goldPriceInteger() {
				return Math.floor(this.goldPrice).toString();
			},
			// 黄金价格小数部分
			goldPriceDecimal() {
				const decimal = (this.goldPrice % 1).toFixed(2).substring(2);
				return decimal;
			},
			// 白银价格整数部分
			silverPriceInteger() {
				return Math.floor(this.silverPrice).toString();
			},
			// 白银价格小数部分
			silverPriceDecimal() {
				const decimal = (this.silverPrice % 1).toFixed(2).substring(2);
				return decimal;
			},
			// 当前选中金属的价格
			currentPrice() {
				return this.selectedMetal === 'gold' ? this.goldPrice.toFixed(2) : this.silverPrice.toFixed(2);
			},
			// 保证金费率
			depositRate() {
				return this.form.depositType === '5' ? 20 : 25;
			}
		},
		onLoad() {
			this.getPrices(); // 初始获取价格
			this.startPriceTimer(); // 启动定时器
		},
		onUnload() {
			// 页面卸载时清除定时器
			if (this.priceTimer) {
				clearInterval(this.priceTimer);
				this.priceTimer = null;
			}
		},
		methods: {
			// 获取黄金和白银价格
			async getPrices() {
				try {
					const response = await uni.request({
						url: 'https://goldapi.szzjtech.cn/jjgold',
						method: 'GET',
						timeout: 10000
					});

					// uni.request返回的是数组，第二个元素是实际响应数据
					const data = response[1];

					if (data && data.statusCode === 200 && data.data && data.data.data.items) {
						const items = data.data.data.items;

						// 黄金回购价格 (item[1] 的 bidprice)
						if (items['1']) {
							const newGoldPrice = parseFloat(items['1'].bidprice) || 545.60;
							this.animatePrice('gold', newGoldPrice);
						}

						// 白银回购价格 (item[2] 的 bidprice)
						if (items['2']) {
							const newSilverPrice = parseFloat(items['2'].bidprice) || 6.88;
							this.animatePrice('silver', newSilverPrice);
						}
					}
				} catch (error) {
					console.error('获取价格失败:', error);
				}
			},
			// 启动价格刷新定时器
			startPriceTimer() {
				// 每5秒刷新一次价格
				this.priceTimer = setInterval(() => {
					this.getPrices();
				}, 5000);
			},
			// 价格动画更新
			animatePrice(type, newPrice) {
				const currentPrice = type === 'gold' ? this.goldPrice : this.silverPrice;

				if (currentPrice !== newPrice) {
					// 更新价格值
					if (type === 'gold') {
						this.goldPrice = newPrice;
					} else {
						this.silverPrice = newPrice;
					}

					// 触发动画
					this.$nextTick(() => {
						const animateProperty = type === 'gold' ? 'goldPriceAnimate' : 'silverPriceAnimate';
						this[animateProperty] = true;

						// 动画结束后移除类
						setTimeout(() => {
							this[animateProperty] = false;
						}, 600);
					});
				}
			},
			// 选择金属类型
			selectMetal(metalType) {
				this.selectedMetal = metalType;
				// 重新计算保证金
				this.calculateDeposit();
			},
			// 切换Tabs
			handleTabChange(item) {
				this.currentTab = item.index;
				// 如果切换到订单管理，则加载数据
				if (this.currentTab === 1) {
					this.resetOrderList();
					this.getOrderList();
				}
			},
			// 切换保证金类型
			selectDepositType(name) {
				this.form.depositType = name;
				this.calculateDeposit();
			},
			// 计算预估保证金
			calculateDeposit() {
				if (!this.form.weight || !this.form.depositType) {
					this.estimatedDeposit = '0.00';
					return;
				}
				const weight = parseFloat(this.form.weight);
				const rate = this.form.depositType === '5' ? 20 : 25;
				this.estimatedDeposit = (weight * rate).toFixed(2);
			},
			// 锁定价格
			lockPrice() {
				// 表单验证
				if (!this.form.weight) {
					uni.showToast({
						title: '请输入预估回收克重',
						icon: 'none'
					});
					return;
				}
				if (parseFloat(this.form.weight) <= 0) {
					uni.showToast({
						title: '回收克重必须大于0',
						icon: 'none'
					});
					return;
				}
				if (!this.form.depositType) {
					uni.showToast({
						title: '请选择保价期限',
						icon: 'none'
					});
					return;
				}

				// 显示确认弹框
				this.showLockPriceModal = true;
			},

			// 关闭锁定价格弹框
			closeLockPriceModal() {
				this.showLockPriceModal = false;
			},

			// 确认锁定价格
			async confirmLockPrice() {
				if (this.submitting) return;

				this.submitting = true;
				try {
					const orderData = {
						metalType: this.selectedMetal === 'gold' ? 'GOLD' : 'SILVER',
						declaredWeightGrams: parseFloat(this.form.weight),
						validityPeriodDays: parseInt(this.form.depositType),
						depositRate: this.depositRate
					};

					const response = await addRecyclingOrder(orderData);

					if (response.code === 200) {
						uni.showToast({
							title: '订单创建成功',
							icon: 'success'
						});
						this.showLockPriceModal = false;
						// 重置表单
						this.form.weight = '';
						this.form.depositType = '5';
						this.estimatedDeposit = '0.00';
						// 如果当前在订单管理页面，刷新订单列表
						if (this.currentTab === 1) {
							this.resetOrderList();
							this.getOrderList();
						}
					} else {
						uni.showToast({
							title: response.msg || '创建订单失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('创建订单失败:', error);
					uni.showToast({
						title: '创建订单失败，请重试',
						icon: 'none'
					});
				} finally {
					this.submitting = false;
				}
			},
			// 获取订单列表
			async getOrderList() {
				if (this.loading) return;

				this.loading = true;
				this.loadmoreStatus = 'loading';

				try {
					const params = {
						pageNum: this.pageNum,
						pageSize: this.pageSize,
						userId: this.userId
					};

					const response = await listRecyclingOrder(params);

					if (response.code === 200) {
						const newOrders = response.rows || [];

						if (this.pageNum === 1) {
							this.orderList = newOrders;
						} else {
							this.orderList = [...this.orderList, ...newOrders];
						}

						this.total = response.total || 0;

						// 更新加载更多状态
						if (this.orderList.length >= this.total) {
							this.loadmoreStatus = 'nomore';
						} else {
							this.loadmoreStatus = 'loadmore';
						}
					} else {
						uni.showToast({
							title: response.msg || '获取订单列表失败',
							icon: 'none'
						});
						this.loadmoreStatus = 'loadmore';
					}
				} catch (error) {
					console.error('获取订单列表失败:', error);
					uni.showToast({
						title: '获取订单列表失败，请重试',
						icon: 'none'
					});
					this.loadmoreStatus = 'loadmore';
				} finally {
					this.loading = false;
				}
			},

			// 重置订单列表
			resetOrderList() {
				this.orderList = [];
				this.pageNum = 1;
				this.total = 0;
				this.loadmoreStatus = 'loadmore';
			},

			// 加载更多
			loadMore() {
				if (this.loadmoreStatus === 'loadmore' && !this.loading) {
					this.pageNum++;
					this.getOrderList();
				}
			},

			// 获取订单状态文本
			getStatusText(status) {
				const statusMap = {
					'PENDING_SHIPMENT': '待发货',
					'SHIPPED': '已发货',
					'RECEIVED': '已收货',
					'INSPECTING': '检测中',
					'COMPLETED': '已完成',
					'CANCELLED': '已取消',
					'RETURNED': '已退回'
				};
				return statusMap[status] || status;
			},

			// 获取订单状态类型
			getStatusType(status) {
				const typeMap = {
					'PENDING_SHIPMENT': 'warning',
					'SHIPPED': 'primary',
					'RECEIVED': 'info',
					'INSPECTING': 'primary',
					'COMPLETED': 'success',
					'CANCELLED': 'error',
					'RETURNED': 'warning'
				};
				return typeMap[status] || 'info';
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},

			// 提交快递单号
			submitTrackingNo(orderId) {
				uni.showModal({
					title: '提交快递单号',
					editable: true,
					placeholderText: '请输入快递单号',
					success: async (res) => {
						if (res.confirm && res.content) {
							const trackingNumber = res.content.trim();
							if (!trackingNumber) {
								uni.showToast({
									title: '请输入有效的快递单号',
									icon: 'none'
								});
								return;
							}

							try {
								// 调用API更新订单状态和快递单号
								const updateData = {
									id: orderId,
									inboundTrackingNumber: trackingNumber,
									status: 'SHIPPED' // 更新状态为已发货
								};

								const response = await updateRecyclingOrder(updateData);

								if (response.code === 200) {
									uni.showToast({
										title: '提交成功',
										icon: 'success'
									});
									// 刷新订单列表
									this.resetOrderList();
									this.getOrderList();
								} else {
									uni.showToast({
										title: response.msg || '提交失败',
										icon: 'none'
									});
								}
							} catch (error) {
								console.error('提交快递单号失败:', error);
								uni.showToast({
									title: '提交失败，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}

	.panel-title {
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		color: #303133;
	}

	.price-section {
		display: flex;
		gap: 20rpx;
		padding: 20rpx;
		
		.price-card {
			flex: 1;
			padding: 30rpx;
			border-radius: 16rpx;
			color: #fff;
			position: relative;
			cursor: pointer;
			transition: all 0.3s ease;
			border: 3rpx solid transparent;

			&.gold {
				background: linear-gradient(to right, #fce1a3, #e8b96a);

				&.selected {
					border-color: #d4af37;
					box-shadow: 0 0 20rpx rgba(212, 175, 55, 0.5);
					transform: scale(1.02);
				}
			}

			&.silver {
				background: linear-gradient(to right, #e0e0e0, #b0bec5);

				&.selected {
					border-color: #c0c0c0;
					box-shadow: 0 0 20rpx rgba(192, 192, 192, 0.5);
					transform: scale(1.02);
				}
			}
			
			.price-title-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;
				
				.price-title {
					font-size: 28rpx;
					opacity: 0.9;
				}
				
				.price-tag {
					font-size: 22rpx;
					background-color: rgba(255, 255, 255, 0.2);
					padding: 4rpx 10rpx;
					border-radius: 6rpx;
				}
			}
			
			.price-value {
				font-weight: bold;
				transition: all 0.3s ease;
				position: relative;

				.currency {
					font-size: 32rpx;
					margin-right: 4rpx;
				}
				.price-integer {
					font-size: 56rpx;
				}
				.price-decimal {
					font-size: 32rpx;
				}

				// 价格变化动画
				&.price-animate {
					animation: priceChange 0.6s ease-in-out;
				}
			}

			.selection-indicator {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				animation: fadeIn 0.3s ease-in-out;

				.checkmark-text {
					color: #fff;
					font-size: 24rpx;
					font-weight: bold;
					line-height: 1;
				}
			}
		}
	}

	// 选择指示器淡入动画
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.5);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	// 价格变化动画关键帧
	@keyframes priceChange {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		25% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.6;
			color: #f39c12 !important;
		}
		75% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.recycle-panel {
		background-color: #fff;
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		
		.deposit-options {
			display: flex;
			gap: 20rpx;
			width: 100%;
		}
		
		.option-card {
			flex: 1;
			border: 1px solid #eee;
			border-radius: 12rpx;
			padding: 20rpx;
			text-align: center;
			position: relative;
			transition: all 0.2s;
			
			&.active {
				border-color: #c8a77f;
				background-color: #fcf8f2;
			}
			
			.option-title {
				font-size: 30rpx;
				color: #303133;
				font-weight: 500;
				margin-bottom: 10rpx;
			}
			
			.option-desc {
				font-size: 24rpx;
				color: #909399;
			}
		}
		
		.deposit-amount {
			font-size: 36rpx;
			color: #fa3534;
			font-weight: bold;
		}

		.submit-btn {
			margin-top: 30rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}
	
	.process-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
	}
	
	.orders-panel {
		// orders-panel 现在是订单管理页面的根容器，可以添加一些边距
		padding: 20rpx;

		.order-list {
			.order-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;

				.order-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 16rpx;
					border-bottom: 1px solid #f5f5f5;
					margin-bottom: 16rpx;

					.order-id {
						font-size: 24rpx;
						color: #909399;
					}
				}

				.order-body {
					.order-desc {
						font-size: 28rpx;
						color: #303133;
						margin-bottom: 8rpx;
					}
					.order-price {
						font-size: 26rpx;
						color: #c8a77f;
						margin-bottom: 8rpx;
					}
					.order-deposit {
						font-size: 26rpx;
						color: #fa3534;
						margin-bottom: 8rpx;
					}
					.order-time {
						font-size: 24rpx;
						color: #909399;
					}
				}

				.order-footer {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-end;
				}
			}
		}
	}

	// 自定义弹框样式（根据实际效果图设计）
	.custom-modal-overlay {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.4);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}

	.custom-modal {
		background-color: #fff;
		border-radius: 20rpx;
		width: 640rpx;
		max-width: 85%;
		overflow: hidden;
		margin: 0 40rpx;
	}

	.modal-header {
		padding: 48rpx 40rpx 32rpx;
		text-align: center;

		.modal-title {
			font-size: 36rpx;
			color: #000;
			font-weight: 500;
		}
	}

	.modal-content {
		padding: 0 40rpx 40rpx;

		.confirm-row {
			display: flex;
			align-items: flex-start;
			padding: 12rpx 0;
			line-height: 1.6;

			.confirm-label {
				font-size: 32rpx;
				color: #000;
				font-weight: normal;
				white-space: nowrap;
				margin-right: 20rpx;
			}

			.confirm-value {
				font-size: 32rpx;
				color: #000;
				font-weight: normal;
				flex: 1;
				text-align: left;
			}
		}
	}

	.modal-footer {
		display: flex;
		border-top: 1rpx solid #e5e5e5;

		.modal-btn {
			flex: 1;
			padding: 36rpx;
			text-align: center;
			font-size: 36rpx;
			font-weight: normal;

			&.cancel-btn {
				color: #000;
				border-right: 1rpx solid #e5e5e5;

				&:active {
					background-color: #f7f7f7;
				}
			}

			&.confirm-btn {
				color: #007aff;

				&:active {
					background-color: #f7f7f7;
				}
			}
		}
	}
</style>