<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @change="handleTabChange" :scrollable="false" lineWidth="30"></u-tabs>
		
		<view v-show="currentTab === 0">
			<view class="price-section">
				<view class="price-card gold" :class="{ 'selected': selectedMetal === 'gold' }" @click="selectMetal('gold')">
					<view class="price-title-row">
						<text class="price-title">实时金价 (黄金)</text>
						<text class="price-tag">实时变动</text>
					</view>
					<view class="price-value" :class="{ 'price-animate': goldPriceAnimate }">
						<text class="currency">¥</text>
						<text class="price-integer">{{ goldPriceInteger }}</text>
						<text class="price-decimal">.{{ goldPriceDecimal }} /g</text>
					</view>
					<view class="selection-indicator" v-if="selectedMetal === 'gold'">
						<u-icon name="checkmark-circle-fill" color="#fff" size="20"></u-icon>
					</view>
				</view>
				<view class="price-card silver" :class="{ 'selected': selectedMetal === 'silver' }" @click="selectMetal('silver')">
					<view class="price-title-row">
						<text class="price-title">实时银价 (白银)</text>
						<text class="price-tag">实时变动</text>
					</view>
					<view class="price-value" :class="{ 'price-animate': silverPriceAnimate }">
						<text class="currency">¥</text>
						<text class="price-integer">{{ silverPriceInteger }}</text>
						<text class="price-decimal">.{{ silverPriceDecimal }} /g</text>
					</view>
					<view class="selection-indicator" v-if="selectedMetal === 'silver'">
						<u-icon name="checkmark-circle-fill" color="#fff" size="20"></u-icon>
					</view>
				</view>
			</view>

			<view class="recycle-panel">
				<h3 class="panel-title">预约高价回收 ({{ selectedMetal === 'gold' ? '黄金' : '白银' }})</h3>
				<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="auto">
					<u-form-item label="预估回收克重(g)" prop="weight">
						<u--input v-model="form.weight" :placeholder="`请输入您预估的${selectedMetal === 'gold' ? '黄金' : '白银'}克重`" type="digit" @input="calculateDeposit"></u--input>
					</u-form-item>
					
					<u-form-item label="选择保价期限" prop="depositType" labelPosition="top">
						<u-radio-group v-model="form.depositType" @change="calculateDeposit" class="deposit-options">
							<view class="option-card" :class="{'active': form.depositType === '5'}" @click="selectDepositType('5')">
								<view class="option-title">5天内有效</view>
								<view class="option-desc">保证金 20元/克</view>
								<u-radio name="5" :customStyle="{position: 'absolute', top: '10rpx', right: '10rpx'}"></u-radio>
							</view>
							<view class="option-card" :class="{'active': form.depositType === '10'}" @click="selectDepositType('10')">
								<view class="option-title">10天内有效</view>
								<view class="option-desc">保证金 25元/克</view>
								<u-radio name="10" :customStyle="{position: 'absolute', top: '10rpx', right: '10rpx'}"></u-radio>
							</view>
						</u-radio-group>
					</u-form-item>
					
					<u-form-item label="预估保证金">
						<text class="deposit-amount">¥ {{ estimatedDeposit }}</text>
					</u-form-item>
					
					<u-button type="primary" shape="circle" class="submit-btn" text="立即锁定价格" @click="lockPrice"></u-button>
				</u--form>
			</view>

			<view class="process-panel">
				<h3 class="panel-title">回收流程</h3>
				<u-steps :current="0" activeColor="#c8a77f">
					<u-steps-item title="锁定价格"></u-steps-item>
					<u-steps-item title="邮寄黄金"></u-steps-item>
					<u-steps-item title="专业检测"></u-steps-item>
					<u-steps-item title="结算尾款"></u-steps-item>
				</u-steps>
			</view>
		</view>

		<view v-show="currentTab === 1">
			<view class="orders-panel">
				<view class="order-list">
					<view class="order-item">
						<view class="order-header">
							<text class="order-id">订单号: YXHS20250713001</text>
							<u-tag text="待邮寄" type="warning" size="mini"></u-tag>
						</view>
						<view class="order-body">
							<view class="order-desc">预约回收：黄金 约 10.00 克</view>
							<view class="order-time">2025-07-13 10:30</view>
						</view>
						<view class="order-footer">
							<u-button shape="circle" size="small" text="提交快递单号" @click="submitTrackingNo('YXHS20250713001')"></u-button>
						</view>
					</view>
					<view class="order-item">
						<view class="order-header">
							<text class="order-id">订单号: YXHS20250712005</text>
							<u-tag text="检测中" type="primary" size="mini"></u-tag>
						</view>
						<view class="order-body">
							<view class="order-desc">预约回收：黄金 约 25.50 克</view>
							<view class="order-time">2025-07-12 15:00</view>
						</view>
					</view>
					<view class="order-item">
						<view class="order-header">
							<text class="order-id">订单号: YXHS20250710002</text>
							<u-tag text="已完成" type="success" size="mini"></u-tag>
						</view>
						<view class="order-body">
							<view class="order-desc">预约回收：白银 约 100.00 克</view>
							<view class="order-time">2025-07-10 09:45</view>
						</view>
					</view>
					<u-empty v-if="!orderList.length" mode="order" text="暂无回收订单" marginTop="150"></u-empty>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { addRecyclingOrder,listRecyclingOrder } from "@/api/gold/recyclingOrder.js"
	export default {
		data() {
			return {
				tabsList: [{ name: '预约回收' }, { name: '订单管理' }],
				currentTab: 0,
				form: {
					weight: '',
					depositType: '5', // 默认选中5天
				},
				estimatedDeposit: '0.00',
				// 静态订单列表，用于演示
				orderList: [1, 2, 3],
				// 价格相关数据
				goldPrice: 545.60, // 黄金回购价格
				silverPrice: 6.88, // 白银回购价格
				priceTimer: null, // 价格刷新定时器
				goldPriceAnimate: false, // 黄金价格动画状态
				silverPriceAnimate: false, // 白银价格动画状态
				selectedMetal: 'gold', // 选中的金属类型：'gold' 或 'silver'
				userId:this.$store.state.user.userId
			};
		},
		computed: {
			// 黄金价格整数部分
			goldPriceInteger() {
				return Math.floor(this.goldPrice).toString();
			},
			// 黄金价格小数部分
			goldPriceDecimal() {
				const decimal = (this.goldPrice % 1).toFixed(2).substring(2);
				return decimal;
			},
			// 白银价格整数部分
			silverPriceInteger() {
				return Math.floor(this.silverPrice).toString();
			},
			// 白银价格小数部分
			silverPriceDecimal() {
				const decimal = (this.silverPrice % 1).toFixed(2).substring(2);
				return decimal;
			}
		},
		onLoad() {
			this.getPrices(); // 初始获取价格
			this.startPriceTimer(); // 启动定时器
		},
		onUnload() {
			// 页面卸载时清除定时器
			if (this.priceTimer) {
				clearInterval(this.priceTimer);
				this.priceTimer = null;
			}
		},
		methods: {
			// 获取黄金和白银价格
			async getPrices() {
				try {
					const response = await uni.request({
						url: 'https://goldapi.szzjtech.cn/jjgold',
						method: 'GET',
						timeout: 10000
					});

					// uni.request返回的是数组，第二个元素是实际响应数据
					const data = response[1];

					if (data && data.statusCode === 200 && data.data && data.data.data.items) {
						const items = data.data.data.items;

						// 黄金回购价格 (item[1] 的 bidprice)
						if (items['1']) {
							const newGoldPrice = parseFloat(items['1'].bidprice) || 545.60;
							this.animatePrice('gold', newGoldPrice);
						}

						// 白银回购价格 (item[2] 的 bidprice)
						if (items['2']) {
							const newSilverPrice = parseFloat(items['2'].bidprice) || 6.88;
							this.animatePrice('silver', newSilverPrice);
						}
					}
				} catch (error) {
					console.error('获取价格失败:', error);
				}
			},
			// 启动价格刷新定时器
			startPriceTimer() {
				// 每5秒刷新一次价格
				this.priceTimer = setInterval(() => {
					this.getPrices();
				}, 5000);
			},
			// 价格动画更新
			animatePrice(type, newPrice) {
				const currentPrice = type === 'gold' ? this.goldPrice : this.silverPrice;

				if (currentPrice !== newPrice) {
					// 更新价格值
					if (type === 'gold') {
						this.goldPrice = newPrice;
					} else {
						this.silverPrice = newPrice;
					}

					// 触发动画
					this.$nextTick(() => {
						const animateProperty = type === 'gold' ? 'goldPriceAnimate' : 'silverPriceAnimate';
						this[animateProperty] = true;

						// 动画结束后移除类
						setTimeout(() => {
							this[animateProperty] = false;
						}, 600);
					});
				}
			},
			// 选择金属类型
			selectMetal(metalType) {
				this.selectedMetal = metalType;
				// 重新计算保证金
				this.calculateDeposit();
			},
			// 切换Tabs
			handleTabChange(item) {
				this.currentTab = item.index;
				// 如果切换到订单管理且列表为空，则加载数据
				if (this.currentTab === 1 && this.orderList.length === 0) {
					// 在此调用您的 getOrderList() API
					console.log("加载订单列表...");
				}
			},
			// 切换保证金类型
			selectDepositType(name) {
				this.form.depositType = name;
				this.calculateDeposit();
			},
			// 计算预估保证金
			calculateDeposit() {
				if (!this.form.weight || !this.form.depositType) {
					this.estimatedDeposit = '0.00';
					return;
				}
				const weight = parseFloat(this.form.weight);
				const rate = this.form.depositType === '5' ? 20 : 25;
				this.estimatedDeposit = (weight * rate).toFixed(2);
			},
			// 锁定价格
			lockPrice() {
				uni.showToast({
					title: '功能开发中',
					icon: 'none'
				});
			},
			// 提交快递单号
			submitTrackingNo(orderId) {
				uni.showModal({
					title: '提交快递单号',
					editable: true,
					placeholderText: '请输入快递单号',
					success: (res) => {
						if (res.confirm && res.content) {
							console.log(`订单 ${orderId} 提交的快递单号为: ${res.content}`);
							this.$modal.msgSuccess('提交成功');
						}
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}

	.panel-title {
		font-size: 34rpx;
		font-weight: bold;
		margin-bottom: 30rpx;
		color: #303133;
	}

	.price-section {
		display: flex;
		gap: 20rpx;
		padding: 20rpx;
		
		.price-card {
			flex: 1;
			padding: 30rpx;
			border-radius: 16rpx;
			color: #fff;
			position: relative;
			cursor: pointer;
			transition: all 0.3s ease;
			border: 3rpx solid transparent;

			&.gold {
				background: linear-gradient(to right, #fce1a3, #e8b96a);

				&.selected {
					border-color: #d4af37;
					box-shadow: 0 0 20rpx rgba(212, 175, 55, 0.5);
					transform: scale(1.02);
				}
			}

			&.silver {
				background: linear-gradient(to right, #e0e0e0, #b0bec5);

				&.selected {
					border-color: #c0c0c0;
					box-shadow: 0 0 20rpx rgba(192, 192, 192, 0.5);
					transform: scale(1.02);
				}
			}
			
			.price-title-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10rpx;
				
				.price-title {
					font-size: 28rpx;
					opacity: 0.9;
				}
				
				.price-tag {
					font-size: 22rpx;
					background-color: rgba(255, 255, 255, 0.2);
					padding: 4rpx 10rpx;
					border-radius: 6rpx;
				}
			}
			
			.price-value {
				font-weight: bold;
				transition: all 0.3s ease;
				position: relative;

				.currency {
					font-size: 32rpx;
					margin-right: 4rpx;
				}
				.price-integer {
					font-size: 56rpx;
				}
				.price-decimal {
					font-size: 32rpx;
				}

				// 价格变化动画
				&.price-animate {
					animation: priceChange 0.6s ease-in-out;
				}
			}

			.selection-indicator {
				position: absolute;
				top: 20rpx;
				right: 20rpx;
				background-color: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				animation: fadeIn 0.3s ease-in-out;
			}
		}
	}

	// 选择指示器淡入动画
	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: scale(0.5);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	// 价格变化动画关键帧
	@keyframes priceChange {
		0% {
			transform: scale(1);
			opacity: 1;
		}
		25% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		50% {
			transform: scale(1.1);
			opacity: 0.6;
			color: #f39c12 !important;
		}
		75% {
			transform: scale(1.05);
			opacity: 0.8;
		}
		100% {
			transform: scale(1);
			opacity: 1;
		}
	}

	.recycle-panel {
		background-color: #fff;
		margin: 0 20rpx 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
		
		.deposit-options {
			display: flex;
			gap: 20rpx;
			width: 100%;
		}
		
		.option-card {
			flex: 1;
			border: 1px solid #eee;
			border-radius: 12rpx;
			padding: 20rpx;
			text-align: center;
			position: relative;
			transition: all 0.2s;
			
			&.active {
				border-color: #c8a77f;
				background-color: #fcf8f2;
			}
			
			.option-title {
				font-size: 30rpx;
				color: #303133;
				font-weight: 500;
				margin-bottom: 10rpx;
			}
			
			.option-desc {
				font-size: 24rpx;
				color: #909399;
			}
		}
		
		.deposit-amount {
			font-size: 36rpx;
			color: #fa3534;
			font-weight: bold;
		}

		.submit-btn {
			margin-top: 30rpx;
			background: linear-gradient(to right, #d4b58e, #c8a77f);
			border: none;
		}
	}
	
	.process-panel {
		background-color: #fff;
		margin: 20rpx;
		padding: 30rpx;
		border-radius: 16rpx;
	}
	
	.orders-panel {
		// orders-panel 现在是订单管理页面的根容器，可以添加一些边距
		padding: 20rpx;
		
		.order-list {
			.order-item {
				background-color: #fff;
				border-radius: 12rpx;
				padding: 20rpx;
				margin-bottom: 20rpx;
				
				.order-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					padding-bottom: 16rpx;
					border-bottom: 1px solid #f5f5f5;
					margin-bottom: 16rpx;
					
					.order-id {
						font-size: 24rpx;
						color: #909399;
					}
				}
				
				.order-body {
					.order-desc {
						font-size: 28rpx;
						color: #303133;
						margin-bottom: 10rpx;
					}
					.order-time {
						font-size: 24rpx;
						color: #909399;
					}
				}
				
				.order-footer {
					margin-top: 20rpx;
					display: flex;
					justify-content: flex-end;
				}
			}
		}
	}
</style>