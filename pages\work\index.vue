<template>
	<view class="page-container">
		<u-tabs :list="tabsList" :current="currentTab" @click="handleTabClick" :scrollable="false"></u-tabs>
		
		<view class="list-wrapper" v-show="currentTab === 0">
			<view class="flex-waterfall">
				<view class="column">
					<view class="waterfall-item" v-for="(item) in leftList" :key="item.requestId">
						<image class="item-image" :src="item.fullImageUrl" mode="widthFix"></image>
						<view class="item-desc">{{ item.description }}</view>
					</view>
				</view>
				<view class="column">
					<view class="waterfall-item" v-for="(item) in rightList" :key="item.requestId">
						<image class="item-image" :src="item.fullImageUrl" mode="widthFix"></image>
						<view class="item-desc">{{ item.description }}</view>
					</view>
				</view>
			</view>
			<u-loadmore :status="loadStatus" marginTop="20" />
		</view>
		
		<view class="form-wrapper" v-if="currentTab === 1">
			<u--form :model="form" ref="uForm" labelPosition="left" labelWidth="100">
				
				<u-form-item label="上传产品图" prop="productImage" borderBottom>
					<view class="manual-uploader" @click="chooseProductImage">
						<image v-if="form.productImage" class="preview-image" :src="form.productImage" mode="aspectFill"></image>
						<u-icon v-else name="plus" color="#d3d3d3" size="30"></u-icon>
						<view v-if="form.productImage" class="delete-icon" @click.stop="deleteProductImage">
							<u-icon name="close-circle-fill" color="rgba(0,0,0,0.5)" size="18"></u-icon>
						</view>
					</view>
				</u-form-item>

				<u-form-item label="购买件数" prop="purchaseQuantity" borderBottom>
					<u--input v-model="form.purchaseQuantity" placeholder="请输入数量" type="number" border="none"></u--input>
				</u-form-item>
				
				<u-form-item label="克重要求" prop="weightRequirement" borderBottom>
					<u--input v-model="form.weightRequirement" placeholder="请输入克重" type="digit" border="none"></u--input>
				</u-form-item>

				<u-form-item label="产品类型" prop="productType" labelWidth="auto">
					<view class="tags-wrapper">
						<view 
							v-for="(item, index) in productTypeList" 
							:key="index" 
							class="tag-item"
							:class="{ 'active': item.selected }"
							@click="toggleProductType(item)"
						>
							{{ item.name }}
						</view>
					</view>
				</u-form-item>
				
				<u-form-item label="留言备注" prop="remark" borderBottom>
					<u--input v-model="form.remark" placeholder="请输入备注 (可选)" border="none"></u--input>
				</u-form-item>

				<u-form-item label="微信号" prop="wechatId" borderBottom>
					<u--input v-model="form.wechatId" placeholder="填写您的微信号, 方便联系" border="none"></u--input>
				</u-form-item>
				
				<u-form-item label="微信二维码" prop="wechatQrcode">
					<view class="manual-uploader" @click="chooseQrcodeImage">
						<image v-if="form.wechatQrcode" class="preview-image" :src="form.wechatQrcode" mode="aspectFill"></image>
						<u-icon v-else name="plus" color="#d3d3d3" size="30"></u-icon>
						<view v-if="form.wechatQrcode" class="delete-icon" @click.stop="deleteQrcodeImage">
							<u-icon name="close-circle-fill" color="rgba(0,0,0,0.5)" size="18"></u-icon>
						</view>
					</view>
				</u-form-item>

			</u--form>

			<view class="submit-button-wrapper">
				<u-button type="primary" shape="circle" @click="handleSubmit" class="submit-btn" :loading="isSubmitting">发布找货</u-button>
			</view>
		</view>
		
		<view class="history-list-wrapper" v-show="currentTab === 2">
			<view class="history-item" v-for="item in historyList" :key="item.requestId">
				<image class="history-item-image" :src="item.fullImageUrl" mode="aspectFill"></image>
				<view class="history-item-info">
					<view class="history-item-desc">{{ item.description }}</view>
					<view class="history-item-time">{{ item.createTime }}</view>
				</view>
				<view class="history-item-status">
					<u-tag :text="statusFilter(item.status).text" :type="statusFilter(item.status).type" size="mini"></u-tag>
				</view>
			</view>
			<u-loadmore :status="historyLoadStatus" marginTop="20" v-if="historyList.length > 0" />
			<u-empty mode="history" text="您还没有发布过找货信息" marginTop="100" v-if="historyList.length === 0 && historyLoadStatus === 'nomore'"></u-empty>
		</view>

	</view>
</template>

<script>
	import { listFindGoodsRequest, addFindGoodsRequest } from "@/api/gold/findGoodsRequest.js"
	import { baseUrl } from "../../config";
	import { getToken } from "../../utils/auth";
	
	export default {
		data() {
			return {

				currentTab: 0, 
				userId: this.$store.state.user.userId,
				
				// "大家在找" 列表相关
				findList: [],
				queryParams: { pageNum: 1, pageSize: 10 },
				total: 0,
				loadStatus: 'loadmore',
				
				// "找货历史" 列表相关
				historyList: [],
				historyQueryParams: { pageNum: 1, pageSize: 10, userId: this.$store.state.user.userId },
				historyTotal: 0,
				historyLoadStatus: 'loadmore',
				
				// "我要找货" 表单相关
				isSubmitting: false, 
				form: {
					productImage: '', 
					purchaseQuantity: '',
					weightRequirement: '',
					productType: '', 
					remark: '',
					wechatId: '',
					wechatQrcode: '',
				},
				productTypeList: [
					{ name: '戒指', selected: false }, { name: '金条/金币', selected: false },
					{ name: '套链', selected: false }, { name: '吊坠', selected: false },
					{ name: '手镯', selected: false }, { name: '耳饰', selected: false },
					{ name: '项链', selected: false }, { name: '手链', selected: false },
					{ name: '手串', selected: false }, { name: '其他', selected: false },
				],
			}
		},
		computed: {
			// 动态设置tabs列表，根据登录状态禁用"找货历史"
			tabsList() {
				return [
					{ name: '大家在找' },
					{ name: '我要找货' },
					{ name: '找货历史', disabled: !this.userId }
				];
			},
			leftList() {
				return this.findList.filter((item, index) => index % 2 === 0);
			},
			rightList() {
				return this.findList.filter((item, index) => index % 2 === 1);
			},
			// 状态过滤器
			statusFilter() {
				return (status) => {
					switch (status) {
						case '0': return { text: '待处理', type: 'warning' };
						case '1': return { text: '处理中', type: 'primary' };
						case '2': return { text: '已完成', type: 'success' };
						default: return { text: '未知', type: 'info' };
					}
				}
			}
		},
		onLoad() {
			this.getFindList();
		},
		onPullDownRefresh() {
			if (this.currentTab === 0) {
				this.refreshFindList().finally(() => uni.stopPullDownRefresh());
			} else if (this.currentTab === 2 && this.userId) {
				this.refreshHistoryList().finally(() => uni.stopPullDownRefresh());
			} else {
				uni.stopPullDownRefresh();
			}
		},
		onReachBottom() {
			if (this.currentTab === 0) {
				if (this.findList.length >= this.total && this.total > 0) {
					this.loadStatus = 'nomore';
					return;
				}
				this.queryParams.pageNum++;
				this.getFindList();
			} else if (this.currentTab === 2 && this.userId) {
				if (this.historyList.length >= this.historyTotal) {
					this.historyLoadStatus = 'nomore';
					return;
				}
				this.historyQueryParams.pageNum++;
				this.getHistoryList();
			}
		},
		methods: {
			// 处理tab点击事件
			handleTabClick(item) {


				// 在切换前检查“找货历史”的登录状态
				if (item.index === 2 && !this.userId) {
					// 保存当前tab状态，用于取消时恢复
					const previousTab = this.currentTab;

					uni.showModal({
						title: '提示',
						content: '您还未登录，是否前往登录？',
						success: (res) => {
							if (res.confirm) {
								this.$tab.navigateTo('/pages/login');
							} else {
								// 用户点击取消，恢复到之前的tab状态
								this.currentTab = previousTab;
							}
						}
					});
					return; // 提前退出，不执行后续的切换操作
				}

				// 所有检查通过，正式切换Tab
				this.currentTab = item.index;

				// 切换到对应tab且列表为空时，加载数据
				if (this.currentTab === 2 && this.historyList.length === 0 && this.userId) {
					this.getHistoryList();
				}
			},

			// --- 刷新与获取数据的方法保持不变 ---
			refreshFindList() {
				this.queryParams.pageNum = 1;
				this.findList = [];
				return this.getFindList();
			},
			getFindList() {
				return new Promise((resolve, reject) => {
					this.loadStatus = 'loading';
					listFindGoodsRequest(this.queryParams).then(res => {
						if (res.code === 200) {
							const processedRows = res.rows.map(item => {
								let desc = '';
								if (item.purchaseQuantity) desc += `${item.purchaseQuantity}件`;
								if (item.weightRequirement) desc += ` ${item.weightRequirement}克的`;
								if (item.productType) desc += ` ${item.productType}`;
								return { ...item, fullImageUrl: baseUrl + item.productImage, description: desc.trim() || '找货需求' };
							});
							this.findList = this.findList.concat(processedRows);
							this.total = res.total;
							this.loadStatus = this.findList.length >= this.total ? 'nomore' : 'loadmore';
							resolve(res);
						} else { this.loadStatus = 'loadmore'; reject(); }
					}).catch((err) => { this.loadStatus = 'loadmore'; reject(err); });
				});
			},

			refreshHistoryList() {
				this.historyQueryParams.pageNum = 1;
				this.historyList = [];
				return this.getHistoryList();
			},
			getHistoryList() {
				return new Promise((resolve, reject) => {
					this.historyLoadStatus = 'loading';
					listFindGoodsRequest(this.historyQueryParams).then(res => {
						if (res.code === 200) {
							const processedRows = res.rows.map(item => {
								let desc = '';
								if (item.purchaseQuantity) desc += `${item.purchaseQuantity}件`;
								if (item.weightRequirement) desc += ` ${item.weightRequirement}克的`;
								if (item.productType) desc += ` ${item.productType}`;
								return { ...item, fullImageUrl: baseUrl + item.productImage, description: desc.trim() || '找货需求' };
							});
							this.historyList = this.historyList.concat(processedRows);
							this.historyTotal = res.total;
							this.historyLoadStatus = this.historyList.length >= this.historyTotal ? 'nomore' : 'loadmore';
							resolve(res);
						} else { this.historyLoadStatus = 'loadmore'; reject(); }
					}).catch((err) => { this.historyLoadStatus = 'loadmore'; reject(err); });
				});
			},
			
			// --- "我要找货" 的方法保持不变 ---
			toggleProductType(clickedItem) {
				this.productTypeList.forEach(item => { item.selected = item.name === clickedItem.name; });
				this.form.productType = clickedItem.name;
			},
			chooseProductImage() {
				uni.chooseImage({ count: 1, sizeType: ['compressed'], success: (res) => { this.form.productImage = res.tempFilePaths[0]; }});
			},
			chooseQrcodeImage() {
				uni.chooseImage({ count: 1, sizeType: ['compressed'], success: (res) => { this.form.wechatQrcode = res.tempFilePaths[0]; }});
			},
			deleteProductImage() { this.form.productImage = ''; },
			deleteQrcodeImage() { this.form.wechatQrcode = ''; },
			async handleSubmit() {
				if (this.isSubmitting) return;
				if (!this.form.productImage) return this.$modal.msgError("请上传产品图");
				if (!this.form.productType) return this.$modal.msgError("请选择一个产品类型");
				if (!this.form.wechatId) return this.$modal.msgError("请输入微信号");
				this.isSubmitting = true;
				this.$modal.loading("正在发布...");
				try {
					const payload = { ...this.form };
					if (payload.productImage && (payload.productImage.startsWith('http://tmp') || payload.productImage.startsWith('wxfile://'))) {
						payload.productImage = await this.uploadFile(payload.productImage);
					}
					if (payload.wechatQrcode && (payload.wechatQrcode.startsWith('http://tmp') || payload.wechatQrcode.startsWith('wxfile://'))) {
						payload.wechatQrcode = await this.uploadFile(payload.wechatQrcode);
					}
					const response = await addFindGoodsRequest(payload);
					this.$modal.closeLoading();
					if (response.code === 200) {
						this.$modal.msgSuccess("发布成功！");
						this.resetForm();
						this.refreshFindList(); 
						setTimeout(() => {
							this.handleTabClick({ index: 2 }); // 模拟点击切换到历史
						}, 1000);
					} else {
						this.$modal.msgError(response.msg || "发布失败");
					}
				} catch (error) {
					this.$modal.closeLoading();
					this.$modal.msgError(error.message || "操作失败，请重试");
				} finally {
					this.isSubmitting = false;
				}
			},
			resetForm(){
				this.form = {
					productImage: '', purchaseQuantity: '', weightRequirement: '', productType: '',
					remark: '', wechatId: '', wechatQrcode: '',
				};
				this.productTypeList.forEach(item => item.selected = false);
			},
			uploadFile(filePath) {
				return new Promise((resolve, reject) => {
					uni.uploadFile({
						url: baseUrl + "/common/upload", filePath: filePath, name: 'file',
						header: { Authorization: 'Bearer ' + getToken() },
						success: (uploadRes) => {
							const data = JSON.parse(uploadRes.data);
							if (data.code === 200) { resolve(data.fileName); } 
							else { reject(new Error(data.msg || '图片上传失败')); }
						},
						fail: (err) => { reject(new Error('网络请求错误，图片上传失败')); }
					});
				});
			}
		}
	}
</script>

<style lang="scss">
	.page-container {
		background-color: #f7f7f7;
		min-height: 100vh;
	}
	
	// "大家在找" 列表样式
	.list-wrapper {
		padding: 10rpx;
	}
	.flex-waterfall {
		display: flex;
		flex-direction: row;
		.column {
			display: flex; flex: 1; flex-direction: column; height: 100%;
			&:first-child { padding-right: 10rpx; }
			&:last-child { padding-left: 10rpx; }
		}
	}
	.waterfall-item {
		background-color: #fff; border-radius: 8px; overflow: hidden;
		margin-bottom: 20rpx; box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.06);
		.item-image { width: 100%; display: block; }
		.item-desc { padding: 16rpx; font-size: 26rpx; color: #303133; }
	}
	
	// "我要找货" 表单样式
	.form-wrapper {
		background-color: #fff; margin: 20rpx; padding: 10rpx 30rpx 40rpx; border-radius: 16rpx;
	}
	.manual-uploader {
		width: 100px; height: 100px; background-color: #f7f8fa; border: 1px dashed #d9d9d9;
		border-radius: 4px; display: flex; justify-content: center; align-items: center;
		position: relative; overflow: hidden;
		.preview-image { width: 100%; height: 100%; }
		.delete-icon {
			position: absolute; top: 0; right: 0; background-color: #f7f8fa; border-radius: 0 0 0 12px;
			padding: 2px 2px 4px 4px; display: flex; justify-content: center; align-items: center;
		}
	}
	.tags-wrapper {
		display: flex; flex-wrap: wrap; gap: 20rpx; padding: 20rpx 0;
		.tag-item {
			background-color: #f5f5f5; color: #606266; padding: 10rpx 24rpx;
			border-radius: 50rpx; font-size: 26rpx; transition: all 0.2s;
			&.active { background-color: #c8a77f; color: #fff; font-weight: 500; }
		}
	}
	.submit-button-wrapper {
		margin-top: 60rpx;
		.submit-btn { background: linear-gradient(to right, #d4b58e, #c8a77f); border: none; }
	}
	
	// "找货历史" 列表样式
	.history-list-wrapper {
		padding: 20rpx;
	}
	.history-item {
		display: flex;
		align-items: center;
		background-color: #fff;
		padding: 20rpx;
		border-radius: 8px;
		margin-bottom: 20rpx;
		position: relative;
		
		.history-item-image {
			width: 120rpx;
			height: 120rpx;
			border-radius: 6px;
			margin-right: 20rpx;
			flex-shrink: 0;
		}
		
		.history-item-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			
			.history-item-desc {
				font-size: 28rpx;
				color: #303133;
				margin-bottom: 10rpx;
			}
			
			.history-item-time {
				font-size: 24rpx;
				color: #909399;
			}
		}
		
		.history-item-status {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
		}
	}
	
	.placeholder-wrapper {
		padding-top: 200rpx;
	}
</style>