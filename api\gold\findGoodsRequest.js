import request from '@/utils/request'

// 查询找货请求列表
export function listFindGoodsRequest(query) {
  return request({
    url: '/gold/findGoodsRequest/list',
    method: 'get',
    params: query
  })
}

// 查询找货请求详细
export function getFindGoodsRequest(requestId) {
  return request({
    url: '/gold/findGoodsRequest/' + requestId,
    method: 'get'
  })
}

// 新增找货请求
export function addFindGoodsRequest(data) {
  return request({
    url: '/gold/findGoodsRequest',
    method: 'post',
    data: data
  })
}

// 修改找货请求
export function updateFindGoodsRequest(data) {
  return request({
    url: '/gold/findGoodsRequest',
    method: 'put',
    data: data
  })
}

// 删除找货请求
export function delFindGoodsRequest(requestId) {
  return request({
    url: '/gold/findGoodsRequest/' + requestId,
    method: 'delete'
  })
}
